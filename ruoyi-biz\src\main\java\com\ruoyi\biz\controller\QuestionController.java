package com.ruoyi.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.Question;
import com.ruoyi.biz.service.IQuestionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.biz.dto.QuestionDTO;
import com.ruoyi.biz.converter.QuestionConverter;

/**
 * 题目Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/biz/question")
public class QuestionController extends BaseController
{
    @Autowired
    private IQuestionService questionService;

    @Autowired
    private QuestionConverter questionConverter;

    /**
     * 查询题目列表
     */
    @PreAuthorize("@ss.hasPermi('biz:question:list')")
    @GetMapping("/list")
    public TableDataInfo list(Question question)
    {
        startPage();
        List<Question> list = questionService.selectQuestionList(question);
        return getDataTable(list);
    }

    /**
     * 导出题目列表
     */
    @PreAuthorize("@ss.hasPermi('biz:question:export')")
    @Log(title = "题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Question question)
    {
        List<Question> list = questionService.selectQuestionList(question);
        ExcelUtil<Question> util = new ExcelUtil<Question>(Question.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 获取题目详细信息
     */
    @PreAuthorize("@ss.hasPermi('biz:question:query')")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        Question question = questionService.selectQuestionByQuestionId(questionId);
        if (question != null) {
            QuestionDTO questionDTO = questionConverter.entityToDto(question);
            return success(questionDTO);
        } else {
            return error("题目不存在");
        }
    }

    /**
     * 新增题目
     */
    @PreAuthorize("@ss.hasPermi('biz:question:add')")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody QuestionDTO questionDTO)
    {
        // 验证题目数据
        questionConverter.validateQuestionData(questionDTO);

        // 转换DTO为实体
        Question question = questionConverter.dtoToEntity(questionDTO);

        // 保存题目
        int result = questionService.insertQuestion(question);

        if (result > 0) {
            // 返回创建成功的题目信息
            QuestionDTO responseDTO = questionConverter.entityToDto(question);
            return AjaxResult.success("创建成功", responseDTO);
        } else {
            return AjaxResult.error("创建失败");
        }
    }

    /**
     * 修改题目
     */
    @PreAuthorize("@ss.hasPermi('biz:question:edit')")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody QuestionDTO questionDTO)
    {
        // 验证题目数据
        questionConverter.validateQuestionData(questionDTO);

        // 转换DTO为实体
        Question question = questionConverter.dtoToEntity(questionDTO);

        // 更新题目
        int result = questionService.updateQuestion(question);

        if (result > 0) {
            // 返回更新成功的题目信息
            QuestionDTO responseDTO = questionConverter.entityToDto(question);
            return AjaxResult.success("更新成功", responseDTO);
        } else {
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 删除题目
     */
    @PreAuthorize("@ss.hasPermi('biz:question:remove')")
    @Log(title = "题目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(questionService.deleteQuestionByQuestionIds(questionIds));
    }

    /**
     * 获取题库统计信息
     */
    @PreAuthorize("@ss.hasPermi('biz:question:list')")
    @GetMapping("/statistics/{bankId}")
    public AjaxResult getStatistics(@PathVariable("bankId") Long bankId)
    {
        return success(questionService.getQuestionStatistics(bankId));
    }

    /**
     * 批量导入题目
     */
    // @PreAuthorize("@ss.hasPermi('biz:question:add')")  // 临时注释权限检查
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping("/batchImport")
    public AjaxResult batchImport(@RequestBody java.util.Map<String, Object> importData)
    {
        try
        {
            Long bankId = Long.valueOf(importData.get("bankId").toString());
            @SuppressWarnings("unchecked")
            java.util.List<java.util.Map<String, Object>> questions =
                (java.util.List<java.util.Map<String, Object>>) importData.get("questions");
            Boolean allowDuplicate = (Boolean) importData.getOrDefault("allowDuplicate", false);
            Boolean reverse = (Boolean) importData.getOrDefault("reverse", false);

            if (questions == null || questions.isEmpty()) {
                return error("没有可导入的题目数据");
            }

            int successCount = 0;
            int failCount = 0;
            java.util.List<String> errors = new java.util.ArrayList<>();
            java.util.Set<String> existingQuestions = new java.util.HashSet<>();

            // 如果不允许重复，先获取现有题目内容用于去重
            if (!allowDuplicate) {
                Question queryQuestion = new Question();
                queryQuestion.setBankId(bankId);
                java.util.List<Question> existingQuestionList = questionService.selectQuestionList(queryQuestion);
                for (Question q : existingQuestionList) {
                    if (q.getQuestionContent() != null) {
                        String normalizedContent = q.getQuestionContent().trim().replaceAll("\\s+", " ");
                        existingQuestions.add(normalizedContent);
                    }
                }
            }

            // 处理题目导入
            for (int i = 0; i < questions.size(); i++) {
                java.util.Map<String, Object> questionData = questions.get(i);
                try {
                    String questionContent = (String) questionData.get("questionContent");
                    if (questionContent == null) {
                        questionContent = (String) questionData.get("content");
                    }

                    // 检查重复
                    if (!allowDuplicate && questionContent != null) {
                        String normalizedContent = questionContent.trim().replaceAll("\\s+", " ");
                        if (existingQuestions.contains(normalizedContent)) {
                            continue; // 跳过重复题目
                        }
                        existingQuestions.add(normalizedContent);
                    }

                    // 转换题目数据为QuestionDTO
                    QuestionDTO questionDTO = convertMapToQuestionDTO(questionData, bankId);

                    // 验证题目数据
                    questionConverter.validateQuestionData(questionDTO);

                    // 转换DTO为实体
                    Question question = questionConverter.dtoToEntity(questionDTO);

                    // 保存题目到数据库
                    int result = questionService.insertQuestion(question);

                    if (result > 0) {
                        successCount++;
                    } else {
                        throw new Exception("数据库保存失败");
                    }

                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "第" + (i + 1) + "题导入失败: " + e.getMessage();
                    errors.add(errorMsg);
                }
            }

            java.util.Map<String, Object> resultData = new java.util.HashMap<>();
            resultData.put("successCount", successCount);
            resultData.put("failCount", failCount);
            resultData.put("errors", errors);
            resultData.put("totalCount", questions.size());
            resultData.put("skippedCount", questions.size() - successCount - failCount);

            String message = String.format("批量导入完成，成功: %d, 失败: %d", successCount, failCount);
            AjaxResult result = AjaxResult.success(message);
            result.put("data", resultData);
            return result;
        }
        catch (Exception e)
        {
            return error("批量导入失败: " + e.getMessage());
        }
    }

    /**
     * 将Map数据转换为QuestionDTO
     */
    private QuestionDTO convertMapToQuestionDTO(java.util.Map<String, Object> questionData, Long bankId) {
        QuestionDTO dto = new QuestionDTO();

        // 设置基本信息
        dto.setBankId(bankId);

        // 获取题目内容
        String questionContent = (String) questionData.get("questionContent");
        if (questionContent == null) {
            questionContent = (String) questionData.get("content");
        }
        dto.setQuestionContent(questionContent);

        // 获取题型
        String questionType = (String) questionData.get("questionType");
        if (questionType == null) {
            questionType = (String) questionData.get("type");
        }
        dto.setQuestionType(questionType);

        // 获取难度
        String difficulty = (String) questionData.get("difficulty");
        if (difficulty == null || difficulty.trim().isEmpty()) {
            difficulty = "中等"; // 默认难度
        }
        dto.setDifficulty(difficulty);

        // 获取解析
        String explanation = (String) questionData.get("explanation");
        if (explanation == null) {
            explanation = (String) questionData.get("analysis");
        }
        dto.setExplanation(explanation);

        // 获取正确答案
        String correctAnswer = (String) questionData.get("correctAnswer");
        dto.setCorrectAnswer(correctAnswer);

        // 处理选项
        @SuppressWarnings("unchecked")
        java.util.List<java.util.Map<String, Object>> optionsList =
            (java.util.List<java.util.Map<String, Object>>) questionData.get("options");
        if (optionsList != null && !optionsList.isEmpty()) {
            java.util.List<QuestionDTO.OptionDTO> options = new java.util.ArrayList<>();
            for (java.util.Map<String, Object> optionMap : optionsList) {
                String optionKey = (String) optionMap.get("optionKey");
                if (optionKey == null) {
                    optionKey = (String) optionMap.get("key");
                }
                String optionContent = (String) optionMap.get("optionContent");
                if (optionContent == null) {
                    optionContent = (String) optionMap.get("content");
                }

                // 判断是否为正确答案
                Boolean isCorrect = false;
                if (correctAnswer != null && optionKey != null) {
                    if ("judgment".equals(questionType)) {
                        // 判断题特殊处理
                        if (("true".equals(correctAnswer) && "正确".equals(optionContent)) ||
                            ("false".equals(correctAnswer) && "错误".equals(optionContent))) {
                            isCorrect = true;
                        }
                    } else {
                        // 选择题处理
                        isCorrect = correctAnswer.contains(optionKey);
                    }
                }

                options.add(new QuestionDTO.OptionDTO(optionKey, optionContent, isCorrect));
            }
            dto.setOptions(options);
        } else if ("judgment".equals(questionType)) {
            // 判断题如果没有选项，创建默认的正确/错误选项
            java.util.List<QuestionDTO.OptionDTO> options = new java.util.ArrayList<>();
            boolean isTrue = "true".equals(correctAnswer) || "正确".equals(correctAnswer);
            options.add(new QuestionDTO.OptionDTO("A", "正确", isTrue));
            options.add(new QuestionDTO.OptionDTO("B", "错误", !isTrue));
            dto.setOptions(options);
        }

        return dto;
    }
}
