{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_question", "_questionBank", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "data", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "expandedQuestions", "selectedQuestions", "isAllSelected", "questionFormVisible", "currentQuestionType", "currentQuestionData", "importDrawerVisible", "batchImportVisible", "currentImportMode", "documentContent", "documentHtmlContent", "parsedQuestions", "parseErrors", "allExpanded", "isSettingFromBackend", "documentImportDialogVisible", "rulesDialogVisible", "activeRuleTab", "isUploading", "isParsing", "importOptions", "reverse", "allowDuplicate", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "$store", "getters", "token", "uploadData", "rich<PERSON><PERSON><PERSON>", "editorInitialized", "watch", "handler", "newVal", "trim", "debounceParseDocument", "immediate", "_this", "clearImportContent", "$nextTick", "initRichEditor", "destroy", "created", "initPage", "debounce", "parseDocument", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "default", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImportClick", "handleAddQuestion", "type", "toggleExpandAll", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "map", "q", "questionId", "success", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "_this5", "question", "handleEditQuestion", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this6", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "showDocumentImportDialog", "_this7", "uploadComponent", "$refs", "documentUpload", "clearFiles", "showRulesDialog", "copyExampleToEditor", "_this8", "htmlTemplate", "setData", "downloadWordTemplate", "download", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this9", "code", "setTimeout", "questions", "collapsed", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msg", "handleUploadError", "toggleQuestion", "$set", "toggleAllQuestions", "_this0", "confirmImport", "_this1", "importQuestions", "_this10", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "batchImportQuestions", "v", "Error", "a", "_this11", "window", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "_defineProperty2", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "instanceReady", "evt", "editor", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "handleEditorContentChange", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "_this12", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "addEventListener", "target", "append<PERSON><PERSON><PERSON>", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "clearTimeout", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "textContent", "lines", "split", "line", "filter", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "_this13", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "getQuestionTypeName", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel',\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.handleEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              setTimeout(() => {\n                this.handleEditorContentChange()\n              }, 100)\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化\n    handleEditorContentChange() {\n      const rawContent = this.richEditor.getData()\n      const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n      this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n      this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqgBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,aAAA;MACA;MACAC,mBAAA;MACAC,mBAAA;MACAC,mBAAA;MACA;MACAC,mBAAA;MACAC,kBAAA;MACAC,iBAAA;MACA;MACAC,eAAA;MACAC,mBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,2BAAA;MACAC,kBAAA;MACAC,aAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;MACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EAEAC,KAAA;IACA;IACA1B,eAAA;MACA2B,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAvB,oBAAA;UACA;QACA;QAIA,IAAAuB,MAAA,IAAAA,MAAA,CAAAC,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAA5B,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACA4B,SAAA;IACA;IACA;IACAlC,mBAAA;MACA8B,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAI,KAAA;QACA,IAAAJ,MAAA;UACA;UACA,KAAAK,kBAAA;UACA,KAAAC,SAAA;YACAF,KAAA,CAAAG,cAAA;UACA;QACA;UACA;UACA,SAAAX,UAAA;YACA,KAAAA,UAAA,CAAAY,OAAA;YACA,KAAAZ,UAAA;YACA,KAAAC,iBAAA;UACA;QACA;MACA;MACAM,SAAA;IACA;EACA;EAEAM,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAR,qBAAA,QAAAS,QAAA,MAAAC,aAAA;IACA;IACA,KAAAjB,UAAA;MACA/C,MAAA,OAAAA;IACA;IACA,KAAA0C,aAAA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAmB,OAAA,WAAAA,QAAA;IACA;EAAA,CAEA;EAEAC,aAAA,WAAAA,cAAA;IAGA;IACA,SAAAlB,UAAA;MACA,KAAAA,UAAA,CAAAY,OAAA;MACA,KAAAZ,UAAA;IACA;EACA;EACAmB,OAAA;IACA;IACAL,QAAA,WAAAA,SAAA;MACA,IAAAM,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAAtE,MAAA,GAAAoE,kBAAA,CAAApE,MAAA;QAAAC,QAAA,GAAAmE,kBAAA,CAAAnE,QAAA;MACA,KAAAD,MAAA;QACA,KAAAuE,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAAzE,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAA0E,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAxE,WAAA;MACA,IAAAyE,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAvE,YAAA,GAAA4E,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAA3E,KAAA,GAAAgF,QAAA,CAAAhF,KAAA;MACA,GAAAkF,KAAA,WAAAb,KAAA;QAEAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAT,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAA3E,YAAA;QACA,IAAA8E,OAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAA3E,YAAA,GAAA8E,OAAA,CAAAH,eAAA,CAAA3E,YAAA,KAAA2E,eAAA,CAAA3E,YAAA;MACA;;MAEA;MACA,IAAA2E,eAAA,CAAA1E,UAAA;QACA,IAAA8E,aAAA;UACA;UACA;UACA;QACA;QACAJ,eAAA,CAAA1E,UAAA,GAAA8E,aAAA,CAAAJ,eAAA,CAAA1E,UAAA,KAAA0E,eAAA,CAAA1E,UAAA;MACA;;MAEA;MACA+E,MAAA,CAAAC,IAAA,CAAAN,eAAA,EAAAO,OAAA,WAAAC,GAAA;QACA,IAAAR,eAAA,CAAAQ,GAAA,YAAAR,eAAA,CAAAQ,GAAA,cAAAR,eAAA,CAAAQ,GAAA,MAAAC,SAAA;UACA,OAAAT,eAAA,CAAAQ,GAAA;QACA;MACA;MAEA,OAAAR,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,+BAAA,OAAAjG,MAAA,EAAAkF,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAA9F,UAAA,GAAAiF,QAAA,CAAApF,IAAA;MACA,GAAAsF,KAAA,WAAAb,KAAA;QAEA;QACAwB,MAAA,CAAA9F,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IAGA;IACA4F,sBAAA,WAAAA,uBAAA;MACA,KAAA7E,mBAAA;IACA;IACA;IACA8E,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAjF,mBAAA,GAAAiF,IAAA;MACA,KAAAhF,mBAAA;MACA,KAAAF,mBAAA;IACA;IACA;IACAmF,eAAA,WAAAA,gBAAA;MACA,KAAAvF,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAC,iBAAA;MACA;IACA;IAIA;IACAuF,qBAAA,WAAAA,sBAAA;MACA,SAAAtF,iBAAA,CAAAuF,MAAA;QACA,KAAAhC,QAAA,CAAAiC,OAAA;QACA;MACA;MACA,KAAAjC,QAAA,CAAAkC,IAAA,6BAAAC,MAAA,MAAA1F,iBAAA,CAAAuF,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAA1F,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAAD,iBAAA,QAAAT,YAAA,CAAAqG,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAAvC,QAAA,CAAAwC,OAAA,uBAAAL,MAAA,MAAA1F,iBAAA,CAAAuF,MAAA;MACA;QACA;QACA,KAAAvF,iBAAA;QACA,KAAAuD,QAAA,CAAAwC,OAAA;MACA;IACA;IAIA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjG,iBAAA,CAAAuF,MAAA;QACA,KAAAhC,QAAA,CAAAiC,OAAA;QACA;MACA;MAEA,KAAAU,QAAA,+CAAAR,MAAA,MAAA1F,iBAAA,CAAAuF,MAAA;QACAY,iBAAA;QACAC,gBAAA;QACAhB,IAAA;MACA,GAAAlB,IAAA;QACA,IAAAmC,cAAA,GAAAJ,MAAA,CAAAjG,iBAAA,CAAA4F,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;QAAA,CACA;QAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAnC,IAAA;UACA+B,MAAA,CAAA1C,QAAA,CAAAwC,OAAA,6BAAAL,MAAA,CAAAO,MAAA,CAAAjG,iBAAA,CAAAuF,MAAA;UACAU,MAAA,CAAAjG,iBAAA;UACAiG,MAAA,CAAAhG,aAAA;UACAgG,MAAA,CAAAvC,eAAA;UACAuC,MAAA,CAAAtC,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UACAyC,MAAA,CAAA1C,QAAA,CAAAC,KAAA;QACA;MACA,GAAAa,KAAA;QACA4B,MAAA,CAAA1C,QAAA,CAAAkC,IAAA;MACA;IACA;IAEA;IACAgB,oBAAA,WAAAA,qBAAAX,UAAA,EAAAY,QAAA;MACA,IAAAA,QAAA;QACA,UAAA1G,iBAAA,CAAA2G,QAAA,CAAAb,UAAA;UACA,KAAA9F,iBAAA,CAAA4G,IAAA,CAAAd,UAAA;QACA;MACA;QACA,IAAAe,KAAA,QAAA7G,iBAAA,CAAA8G,OAAA,CAAAhB,UAAA;QACA,IAAAe,KAAA;UACA,KAAA7G,iBAAA,CAAA+G,MAAA,CAAAF,KAAA;QACA;MACA;;MAEA;MACA,KAAA5G,aAAA,QAAAD,iBAAA,CAAAuF,MAAA,UAAAhG,YAAA,CAAAgG,MAAA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAlB,UAAA;MAAA,IAAAmB,MAAA;MACA,IAAAJ,KAAA,QAAA9G,iBAAA,CAAA+G,OAAA,CAAAhB,UAAA;MACA,IAAAe,KAAA;QACA;QACA,KAAA9G,iBAAA,CAAAgH,MAAA,CAAAF,KAAA;QACA;QACA,SAAA/G,SAAA;UACA,KAAAA,SAAA;UACA;UACA,KAAAP,YAAA,CAAAsF,OAAA,WAAAqC,QAAA;YACA,IAAAA,QAAA,CAAApB,UAAA,KAAAA,UAAA,KAAAmB,MAAA,CAAAlH,iBAAA,CAAA4G,QAAA,CAAAO,QAAA,CAAApB,UAAA;cACAmB,MAAA,CAAAlH,iBAAA,CAAA6G,IAAA,CAAAM,QAAA,CAAApB,UAAA;YACA;UACA;QACA;MACA;QACA;QACA,KAAA/F,iBAAA,CAAA6G,IAAA,CAAAd,UAAA;MACA;IACA;IACA;IACAqB,kBAAA,WAAAA,mBAAAD,QAAA;MACA,KAAA9G,mBAAA,GAAA8G,QAAA;MACA,KAAA/G,mBAAA,GAAA+G,QAAA,CAAAvH,YAAA;MACA,KAAAO,mBAAA;IACA;IACA;IACAkH,kBAAA,WAAAA,mBAAAF,QAAA;MACA;MACA,IAAAG,cAAA,OAAA9C,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0C,QAAA;QACApB,UAAA;QAAA;QACAwB,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;MAAA,EACA;;MAEA;MACA,KAAArH,mBAAA,GAAAiH,cAAA;MACA,KAAAlH,mBAAA,QAAAuH,2BAAA,CAAAR,QAAA,CAAAvH,YAAA;MACA,KAAAO,mBAAA;IACA;IAEA;IACAwH,2BAAA,WAAAA,4BAAAtC,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA,KAAAA,IAAA;IACA;IACA;IACAuC,oBAAA,WAAAA,qBAAAT,QAAA;MAAA,IAAAU,MAAA;MACA,IAAA/H,eAAA,GAAAqH,QAAA,CAAArH,eAAA,CAAAgI,OAAA;MACA,IAAAC,cAAA,GAAAjI,eAAA,CAAA0F,MAAA,QAAA1F,eAAA,CAAAkI,SAAA,kBAAAlI,eAAA;MACA,KAAAqG,QAAA,0CAAAR,MAAA,CAAAoC,cAAA;QACA3B,iBAAA;QACAC,gBAAA;QACAhB,IAAA;MACA,GAAAlB,IAAA;QACA,IAAAoC,qBAAA,EAAAY,QAAA,CAAApB,UAAA,EAAA5B,IAAA;UACA0D,MAAA,CAAArE,QAAA,CAAAwC,OAAA;UACA6B,MAAA,CAAAlE,eAAA;UACAkE,MAAA,CAAAjE,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UAEAoE,MAAA,CAAArE,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAwE,yBAAA,WAAAA,0BAAA;MACA,KAAA9H,mBAAA;MACA,KAAAwD,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAsE,wBAAA,WAAAA,yBAAA;MACA,KAAA3H,kBAAA;MACA,KAAAD,mBAAA;MACA,KAAAqD,eAAA;MACA,KAAAC,aAAA;IACA;IAIA;IACAuE,iBAAA,WAAAA,kBAAAC,IAAA;MACAA,IAAA;IACA;IAEA;IACA1F,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAjC,eAAA;MACA,KAAAC,mBAAA;;MAEA;MACA,KAAAC,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA,KAAAC,WAAA;MACA,KAAAC,oBAAA;;MAEA;MACA,KAAAI,WAAA;MACA,KAAAC,SAAA;;MAEA;MACA,KAAAC,aAAA;QACAC,OAAA;QACAC,cAAA;MACA;IACA;IAEA;IACA+G,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAApH,WAAA;MACA,KAAAC,SAAA;;MAEA;MACA,KAAAwB,SAAA;QACA,IAAA4F,eAAA,GAAAD,MAAA,CAAAE,KAAA,CAAAC,cAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,UAAA;QACA;MACA;MAEA,KAAA3H,2BAAA;IAEA;IAEA;IACA4H,eAAA,WAAAA,gBAAA;MACA,KAAA1H,aAAA;MACA,KAAAD,kBAAA;IACA;IAEA;IACA4H,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,YAAA,muDAuBAxG,IAAA;;MAEA;MACA,SAAAL,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAA8G,OAAA,CAAAD,YAAA;MAEA;QACA;QACA,KAAAnG,SAAA;UACA,IAAAkG,MAAA,CAAA5G,UAAA,IAAA4G,MAAA,CAAA3G,iBAAA;YACA2G,MAAA,CAAA5G,UAAA,CAAA8G,OAAA,CAAAD,YAAA;UAEA;QACA;MACA;;MAEA;MACA,KAAA9H,kBAAA;;MAEA;MACA,KAAAwC,QAAA,CAAAwC,OAAA;IAGA;IAIA;IACAgD,oBAAA,WAAAA,qBAAA;MACA,KAAAC,QAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MAGA,IAAAC,WAAA,GAAAD,IAAA,CAAA9D,IAAA,kFACA8D,IAAA,CAAA9D,IAAA,4EACA8D,IAAA,CAAAxK,IAAA,CAAA0K,QAAA,aAAAF,IAAA,CAAAxK,IAAA,CAAA0K,QAAA;MACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,WAAA;QACA,KAAA5F,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA6F,OAAA;QACA,KAAA9F,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,KAAAzB,UAAA,CAAA/C,MAAA,QAAAA,MAAA;;MAEA;MACA,KAAAiC,WAAA;MACA,KAAAC,SAAA;MAIA;IACA;IAEA;IACAqI,mBAAA,WAAAA,oBAAApF,QAAA,EAAA+E,IAAA;MAAA,IAAAM,MAAA;MAGA,IAAArF,QAAA,CAAAsF,IAAA;QACA;QACA,KAAAxI,WAAA;QACA,KAAAC,SAAA;;QAIA;QACA,KAAAR,eAAA;QACA,KAAAC,WAAA;;QAEA;QACA+I,UAAA;UACAF,MAAA,CAAA1I,2BAAA;UACA0I,MAAA,CAAAtI,SAAA;QACA;;QAEA;QACA,KAAAL,oBAAA;;QAEA;QACA,IAAAsD,QAAA,CAAAwF,SAAA,IAAAxF,QAAA,CAAAwF,SAAA,CAAApE,MAAA;UACA,KAAA7E,eAAA,GAAAyD,QAAA,CAAAwF,SAAA,CAAA/D,GAAA,WAAAsB,QAAA;YAAA,WAAA3C,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0C,QAAA;cACA0C,SAAA;YAAA;UAAA,CACA;UACA;UACA,KAAAhJ,WAAA;UACA,KAAAD,WAAA,GAAAwD,QAAA,CAAA0F,MAAA;;UAEA;UACA,IAAAC,UAAA,GAAA3F,QAAA,CAAA0F,MAAA,GAAA1F,QAAA,CAAA0F,MAAA,CAAAtE,MAAA;UACA,IAAAuE,UAAA;YACA,KAAAvG,QAAA,CAAAwC,OAAA,mCAAAL,MAAA,CAAAvB,QAAA,CAAAwF,SAAA,CAAApE,MAAA,sCAAAG,MAAA,CAAAoE,UAAA;UACA;YACA,KAAAvG,QAAA,CAAAwC,OAAA,mCAAAL,MAAA,CAAAvB,QAAA,CAAAwF,SAAA,CAAApE,MAAA;UACA;QAGA;UACA,KAAAhC,QAAA,CAAAC,KAAA;UACA,KAAA9C,eAAA;UACA,KAAAC,WAAA,GAAAwD,QAAA,CAAA0F,MAAA;QAGA;;QAEA;QACA,IAAA1F,QAAA,CAAA4F,eAAA;UACA,KAAAC,gBAAA,CAAA7F,QAAA,CAAA4F,eAAA;UACA,KAAAvJ,eAAA,GAAA2D,QAAA,CAAA4F,eAAA;UACA,KAAAtJ,mBAAA,GAAA0D,QAAA,CAAA4F,eAAA;QAEA;;QAEA;QACAL,UAAA;UACAF,MAAA,CAAA3I,oBAAA;QACA;MACA;QAEA,KAAA0C,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAA8F,GAAA;QACA;QACA,KAAAhJ,WAAA;QACA,KAAAC,SAAA;MACA;IACA;IAEA;IACAgJ,iBAAA,WAAAA,kBAAA1G,KAAA,EAAA0F,IAAA;MAEA,KAAA3F,QAAA,CAAAC,KAAA;;MAEA;MACA,KAAAvC,WAAA;MACA,KAAAC,SAAA;IACA;IAIA;IACAiJ,cAAA,WAAAA,eAAAtD,KAAA;MACA,IAAAK,QAAA,QAAAxG,eAAA,CAAAmG,KAAA;MACA,KAAAuD,IAAA,CAAAlD,QAAA,gBAAAA,QAAA,CAAA0C,SAAA;IACA;IAEA;IACAS,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAA1J,WAAA,SAAAA,WAAA;MACA,KAAAF,eAAA,CAAAmE,OAAA,WAAAqC,QAAA;QACAoD,MAAA,CAAAF,IAAA,CAAAlD,QAAA,gBAAAoD,MAAA,CAAA1J,WAAA;MACA;IAEA;IAEA;IACA2J,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAA9J,eAAA,CAAA6E,MAAA;QACA,KAAAhC,QAAA,CAAAiC,OAAA;QACA;MACA;MAEA,KAAAU,QAAA,6BAAAR,MAAA,MAAAhF,eAAA,CAAA6E,MAAA;QACAY,iBAAA;QACAC,gBAAA;QACAhB,IAAA;MACA,GAAAlB,IAAA;QACAsG,MAAA,CAAAC,eAAA;MACA,GAAApG,KAAA;IACA;IAEA;IACAoG,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAC,kBAAA,CAAAnG,OAAA,mBAAAoG,aAAA,CAAApG,OAAA,IAAAqG,CAAA,UAAAC,QAAA;QAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAA7G,QAAA,EAAA8G,EAAA;QAAA,WAAAL,aAAA,CAAApG,OAAA,IAAA0G,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAEA;cACAN,iBAAA,OAAAO,mBAAA,CAAA9G,OAAA,EAAAkG,OAAA,CAAAhK,eAAA;cAEA,IAAAgK,OAAA,CAAAvJ,aAAA,CAAAC,OAAA;gBACA2J,iBAAA,CAAA3J,OAAA;cACA;;cAEA;cACA4J,UAAA;gBACAhM,MAAA,EAAA0L,OAAA,CAAA1L,MAAA;gBACA2K,SAAA,EAAAoB,iBAAA;gBACA1J,cAAA,EAAAqJ,OAAA,CAAAvJ,aAAA,CAAAE;cACA;cAAA8J,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAG,kCAAA,EAAAP,UAAA;YAAA;cAAA7G,QAAA,GAAAgH,QAAA,CAAAK,CAAA;cAAA,MAEArH,QAAA,CAAAsF,IAAA;gBAAA0B,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACAV,OAAA,CAAAnH,QAAA,CAAAwC,OAAA,6BAAAL,MAAA,CAAAqF,iBAAA,CAAAxF,MAAA;cAAA4F,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAA,MAEA,IAAAK,KAAA,CAAAtH,QAAA,CAAA8F,GAAA;YAAA;cAEAS,OAAA,CAAArK,mBAAA;cACAqK,OAAA,CAAAlK,eAAA;cACAkK,OAAA,CAAAjK,mBAAA;cACAiK,OAAA,CAAAhK,eAAA;cACAgK,OAAA,CAAA/J,WAAA;cAIA+J,OAAA,CAAAhH,eAAA;cACAgH,OAAA,CAAA/G,aAAA;cAAAwH,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAK,CAAA;cAGAd,OAAA,CAAAnH,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAA2H,QAAA,CAAAO,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAnI,cAAA,WAAAA,eAAA;MAAA,IAAAgJ,OAAA;MACA,SAAA1J,iBAAA;QACA;MACA;;MAEA;MACA,KAAA2J,MAAA,CAAAC,QAAA;QAEA,KAAAC,kBAAA;QACA;MACA;MAEA;QACA;QACA,SAAA9J,UAAA;UACA,KAAAA,UAAA,CAAAY,OAAA;UACA,KAAAZ,UAAA;QACA;;QAEA;QACA,IAAA+J,eAAA,GAAAC,QAAA,CAAAC,cAAA;QACA,KAAAF,eAAA;UAEA;QACA;;QAEA;QACAA,eAAA,CAAAG,SAAA;;QAEA;QACA,KAAAxJ,SAAA;UACA;UACA,KAAAkJ,MAAA,CAAAC,QAAA,KAAAD,MAAA,CAAAC,QAAA,CAAAhE,OAAA;YAEA8D,OAAA,CAAAQ,kBAAA;YACA;UACA;UAEA;YACA;YACAR,OAAA,CAAA3J,UAAA,GAAA4J,MAAA,CAAAC,QAAA,CAAAhE,OAAA,6BAAAuE,gBAAA,CAAA5H,OAAA,MAAA4H,gBAAA,CAAA5H,OAAA,MAAA4H,gBAAA,CAAA5H,OAAA,MAAA4H,gBAAA,CAAA5H,OAAA;cACA6H,MAAA;cAAA;cACAC,OAAA,GACA;gBAAA5N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,GACA;gBAAA7N,IAAA;gBAAA6N,KAAA;cAAA,EACA;cACAC,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAC,cAAA;cACAC,qBAAA;cACA;cACAC,sBAAA;cACAC,kBAAA;cACA;cACAC,oBAAA,EAAA3L,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACA0L,iBAAA;cACA;cACAC,QAAA;YAAA,wBAEA,uCACA,2BAEA,oCACA;cACAC,aAAA,WAAAA,cAAAC,GAAA;gBACA,IAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA;gBACAA,MAAA,CAAAC,EAAA,yBAAAF,GAAA;kBACA,IAAAG,MAAA,GAAAH,GAAA,CAAAvO,IAAA;kBACA,IAAA0O,MAAA,CAAAC,OAAA;oBACAhE,UAAA;sBACA,IAAAiE,aAAA,GAAAC,WAAA;wBACA;0BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;0BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;4BACAC,aAAA,CAAAN,aAAA;4BACAF,MAAA,CAAAS,UAAA;0BACA;wBACA,SAAAC,CAAA;0BACA;wBAAA;sBAEA;sBACAzE,UAAA;wBAAA,OAAAuE,aAAA,CAAAN,aAAA;sBAAA;oBACA;kBACA;gBACA;cACA;YACA,EACA;UACA,SAAAnK,KAAA;YACAmI,OAAA,CAAAG,kBAAA;YACA;UACA;;UAEA;UACA,IAAAH,OAAA,CAAA3J,UAAA,IAAA2J,OAAA,CAAA3J,UAAA,CAAAwL,EAAA;YACA7B,OAAA,CAAA3J,UAAA,CAAAwL,EAAA;cACA7B,OAAA,CAAAyC,yBAAA;YACA;YAEAzC,OAAA,CAAA3J,UAAA,CAAAwL,EAAA;cACA9D,UAAA;gBACAiC,OAAA,CAAAyC,yBAAA;cACA;YACA;YAEAzC,OAAA,CAAA3J,UAAA,CAAAwL,EAAA;cACA7B,OAAA,CAAA1J,iBAAA;cACA0J,OAAA,CAAA3J,UAAA,CAAA8G,OAAA;YACA;UACA;QACA;MAEA,SAAAtF,KAAA;QACA,KAAAsI,kBAAA;MACA;IACA;IAEA;IACAsC,yBAAA,WAAAA,0BAAA;MACA,IAAAC,UAAA,QAAArM,UAAA,CAAAsM,OAAA;MACA,IAAAC,uBAAA,QAAAC,qBAAA,CAAAH,UAAA;MACA,KAAA5N,mBAAA,QAAAgO,0BAAA,CAAAF,uBAAA;MACA,KAAA/N,eAAA,QAAAkO,uBAAA,CAAAH,uBAAA;IACA;IAEA;IACAzC,kBAAA,WAAAA,mBAAA;MAAA,IAAA6C,OAAA;MACA,IAAA5C,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACA,IAAA6C,QAAA,GAAA5C,QAAA,CAAA6C,aAAA;QACAD,QAAA,CAAAE,SAAA;QACAF,QAAA,CAAAG,WAAA;QACAH,QAAA,CAAAI,KAAA;QACAJ,QAAA,CAAAK,KAAA,CAAAC,OAAA;;QAEA;QACAN,QAAA,CAAAO,gBAAA,oBAAAhB,CAAA;UACAQ,OAAA,CAAAnO,eAAA,GAAA2N,CAAA,CAAAiB,MAAA,CAAAJ,KAAA;UACAL,OAAA,CAAAlO,mBAAA,GAAA0N,CAAA,CAAAiB,MAAA,CAAAJ,KAAA;QACA;QAEAjD,eAAA,CAAAG,SAAA;QACAH,eAAA,CAAAsD,WAAA,CAAAT,QAAA;QACA,KAAA3M,iBAAA;MACA;IACA;IAIA;IACA+H,gBAAA,WAAAA,iBAAAsF,OAAA;MACA,SAAAtN,UAAA,SAAAC,iBAAA;QACA,KAAAD,UAAA,CAAA8G,OAAA,CAAAwG,OAAA;MACA;QACA,KAAA9O,eAAA,GAAA8O,OAAA;QACA,KAAA7O,mBAAA,GAAA6O,OAAA;MACA;IACA;IAIA;IACAvM,QAAA,WAAAA,SAAAwM,IAAA,EAAAC,IAAA;MACA,IAAAC,OAAA;MACA,gBAAAC,iBAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAArK,MAAA,EAAAsK,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;UAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;QAAA;QACA,IAAAC,KAAA,YAAAA,MAAA;UACAC,YAAA,CAAAR,OAAA;UACAF,IAAA,CAAAW,KAAA,SAAAL,IAAA;QACA;QACAI,YAAA,CAAAR,OAAA;QACAA,OAAA,GAAA/F,UAAA,CAAAsG,KAAA,EAAAR,IAAA;MACA;IACA;IAEA;IACAhB,qBAAA,WAAAA,sBAAAc,OAAA;MACA,KAAAA,OAAA,SAAAA,OAAA;;MAEA;MACA,IAAAa,aAAA,GAAAvE,MAAA,CAAAwE,QAAA,CAAAC,MAAA;MACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAtI,OAAA;MAEA,OAAAyH,OAAA,CAAAzH,OAAA,CAAAyI,QAAA;IACA;IAEA;IACAtN,aAAA,WAAAA,cAAA;MACA,UAAAxC,eAAA,CAAA6B,IAAA;QACA,KAAA3B,eAAA;QACA,KAAAC,WAAA;QACA;MACA;MAEA;QACA,IAAA6P,WAAA,QAAAC,oBAAA,MAAAjQ,eAAA;QACA,KAAAE,eAAA,GAAA8P,WAAA,CAAA7G,SAAA,CAAA/D,GAAA,WAAAsB,QAAA;UAAA,WAAA3C,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0C,QAAA;YACA0C,SAAA;UAAA;QAAA,CACA;QACA,KAAAjJ,WAAA,GAAA6P,WAAA,CAAA3G,MAAA;MACA,SAAArG,KAAA;QACA,KAAA7C,WAAA,cAAA6C,KAAA,CAAAkN,OAAA;QACA,KAAAhQ,eAAA;MACA;IACA;IAEA;IACA+P,oBAAA,WAAAA,qBAAAnB,OAAA;MACA,IAAA3F,SAAA;MACA,IAAAE,MAAA;MAEA,KAAAyF,OAAA,WAAAA,OAAA;QAEA;UAAA3F,SAAA,EAAAA,SAAA;UAAAE,MAAA;QAAA;MACA;MAEA;QAGA,IAAA8G,WAAA,QAAAjC,uBAAA,CAAAY,OAAA;QAEA,KAAAqB,WAAA,IAAAA,WAAA,CAAAtO,IAAA,GAAAkD,MAAA;UACA;YAAAoE,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAEA,IAAA+G,KAAA,GAAAD,WAAA,CAAAE,KAAA,OAAAjL,GAAA,WAAAkL,IAAA;UAAA,OAAAA,IAAA,CAAAzO,IAAA;QAAA,GAAA0O,MAAA,WAAAD,IAAA;UAAA,OAAAA,IAAA,CAAAvL,MAAA;QAAA;QAEA,IAAAqL,KAAA,CAAArL,MAAA;UACA;YAAAoE,SAAA,EAAAA,SAAA;YAAAE,MAAA;UAAA;QACA;QAIA,IAAAmH,oBAAA;QACA,IAAAC,cAAA;QAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAArL,MAAA,EAAA2L,CAAA;UACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;UAEA;UACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAN,IAAA,UAAAO,mBAAA,CAAAP,IAAA;UAEA,IAAAK,eAAA;YACA;YACA,IAAAH,oBAAA,CAAAzL,MAAA;cACA;gBACA,IAAA+L,YAAA,GAAAN,oBAAA,CAAAO,IAAA;gBACA,IAAAC,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;gBACA,IAAAO,cAAA;kBACA7H,SAAA,CAAA/C,IAAA,CAAA4K,cAAA;gBACA;cACA,SAAAhO,KAAA;gBACAqG,MAAA,CAAAjD,IAAA,WAAAlB,MAAA,CAAAuL,cAAA,uCAAAvL,MAAA,CAAAlC,KAAA,CAAAkN,OAAA;cACA;YACA;;YAEA;YACAM,oBAAA,IAAAF,IAAA;YACAG,cAAA;UACA;YACA;YACA,IAAAD,oBAAA,CAAAzL,MAAA;cACAyL,oBAAA,CAAApK,IAAA,CAAAkK,IAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAE,oBAAA,CAAAzL,MAAA;UACA;YACA,IAAA+L,aAAA,GAAAN,oBAAA,CAAAO,IAAA;YACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;YACA,IAAAO,eAAA;cACA7H,SAAA,CAAA/C,IAAA,CAAA4K,eAAA;YACA;UACA,SAAAhO,KAAA;YACAqG,MAAA,CAAAjD,IAAA,WAAAlB,MAAA,CAAAuL,cAAA,uCAAAvL,MAAA,CAAAlC,KAAA,CAAAkN,OAAA;UACA;QACA;MAEA,SAAAlN,KAAA;QACAqG,MAAA,CAAAjD,IAAA,0CAAAlB,MAAA,CAAAlC,KAAA,CAAAkN,OAAA;MACA;MAEA;QAAA/G,SAAA,EAAAA,SAAA;QAAAE,MAAA,EAAAA;MAAA;IACA;IAEA;IACAuH,mBAAA,WAAAA,oBAAAN,IAAA;MACA;MACA;MACA;MACA,wBAAAY,IAAA,CAAAZ,IAAA;IACA;IAEA;IACAO,mBAAA,WAAAA,oBAAAP,IAAA;MACA;MACA;MACA,mBAAAY,IAAA,CAAAZ,IAAA;IACA;IAEA;IACAW,sBAAA,WAAAA,uBAAAH,YAAA;MACA,IAAAV,KAAA,GAAAU,YAAA,CAAAT,KAAA,OAAAjL,GAAA,WAAAkL,IAAA;QAAA,OAAAA,IAAA,CAAAzO,IAAA;MAAA,GAAA0O,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAAvL,MAAA;MAAA;MAEA,IAAAqL,KAAA,CAAArL,MAAA;QACA,UAAAkG,KAAA;MACA;MAEA,IAAA9L,YAAA;MACA,IAAAE,eAAA;MACA,IAAA8R,iBAAA;;MAEA;MACA,SAAAT,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAArL,MAAA,EAAA2L,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;QACA,IAAAU,SAAA,GAAAd,IAAA,CAAAe,KAAA;QACA,IAAAD,SAAA;UACA,IAAAE,QAAA,GAAAF,SAAA;;UAEA;UACA,IAAAE,QAAA,CAAAnL,QAAA;YACAhH,YAAA;UACA,WAAAmS,QAAA,CAAAnL,QAAA;YACAhH,YAAA;UACA,WAAAmS,QAAA,CAAAnL,QAAA;YACAhH,YAAA;UACA,WAAAmS,QAAA,CAAAnL,QAAA;YACAhH,YAAA;UACA,WAAAmS,QAAA,CAAAnL,QAAA;YACAhH,YAAA;UACA;;UAEA;UACA,IAAAoS,gBAAA,GAAAjB,IAAA,CAAAjJ,OAAA,iBAAAxF,IAAA;UACA,IAAA0P,gBAAA;YACAlS,eAAA,GAAAkS,gBAAA;YACAJ,iBAAA,GAAAT,CAAA;UACA;YACAS,iBAAA,GAAAT,CAAA;UACA;UACA;QACA;MACA;;MAEA;MACA,IAAAS,iBAAA;QACAA,iBAAA;MACA;;MAEA;MACA,SAAAT,EAAA,GAAAS,iBAAA,EAAAT,EAAA,GAAAN,KAAA,CAAArL,MAAA,EAAA2L,EAAA;QACA,IAAAJ,KAAA,GAAAF,KAAA,CAAAM,EAAA;;QAEA;QACA,SAAAE,mBAAA,CAAAN,KAAA;UACA;UACAjR,eAAA,GAAAiR,KAAA,CAAAjJ,OAAA,uBAAAxF,IAAA;UACAsP,iBAAA,GAAAT,EAAA;UACA;QACA,YAAArR,eAAA;UACA;UACAA,eAAA,GAAAiR,KAAA;UACAa,iBAAA,GAAAT,EAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAA,GAAA,GAAAS,iBAAA,EAAAT,GAAA,GAAAN,KAAA,CAAArL,MAAA,EAAA2L,GAAA;QACA,IAAAJ,MAAA,GAAAF,KAAA,CAAAM,GAAA;;QAEA;QACA,SAAAc,YAAA,CAAAlB,MAAA,UAAAmB,YAAA,CAAAnB,MAAA,KACA,KAAAoB,iBAAA,CAAApB,MAAA,UAAAqB,gBAAA,CAAArB,MAAA;UACA;QACA;;QAEA;QACA,IAAAsB,SAAA,GAAAtB,MAAA;QACA;QACA,SAAAM,mBAAA,CAAAN,MAAA;UACAsB,SAAA,GAAAtB,MAAA,CAAAjJ,OAAA,uBAAAxF,IAAA;QACA;QAEA,IAAA+P,SAAA;UACA,IAAAvS,eAAA;YACAA,eAAA,WAAAuS,SAAA;UACA;YACAvS,eAAA,GAAAuS,SAAA;UACA;QACA;MACA;MAEA,KAAAvS,eAAA;QACA,UAAA4L,KAAA;MACA;;MAEA;MACA,IAAA4G,oBAAA,GAAAxS,eAAA,CAAAwC,IAAA;MACA;MACA,wBAAAqP,IAAA,CAAAW,oBAAA;QACAA,oBAAA,GAAAA,oBAAA,CAAAxK,OAAA,0BAAAxF,IAAA;MACA;;MAEA;MACA,IAAAgQ,oBAAA,CAAA1L,QAAA;QACA0L,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;MACA;MAEA,IAAAnL,QAAA;QACAvH,YAAA,EAAAA,YAAA;QACAyF,IAAA,EAAAzF,YAAA;QACA4S,QAAA,OAAAC,kBAAA,CAAA7S,YAAA;QACAE,eAAA,EAAAwS,oBAAA;QACA/C,OAAA,EAAA+C,oBAAA;QACAzS,UAAA;QAAA;QACA6S,WAAA;QACAC,OAAA;QACAC,aAAA;QACA/I,SAAA;MACA;;MAEA;MACA,IAAAgJ,YAAA,QAAAC,qBAAA,CAAAjC,KAAA;MACA1J,QAAA,CAAAwL,OAAA,GAAAE,YAAA,CAAAF,OAAA;;MAEA;MACA,IAAA/S,YAAA,mBAAAuH,QAAA,CAAAwL,OAAA,CAAAnN,MAAA;QACA;QACA5F,YAAA;QACAuH,QAAA,CAAAvH,YAAA,GAAAA,YAAA;QACAuH,QAAA,CAAA9B,IAAA,GAAAzF,YAAA;QACAuH,QAAA,CAAAqL,QAAA,QAAAC,kBAAA,CAAA7S,YAAA;MACA;;MAEA;MACA,KAAAmT,0BAAA,CAAAlC,KAAA,EAAA1J,QAAA;;MAEA;MACA,IAAAvH,YAAA,iBAAAuH,QAAA,CAAAyL,aAAA,IAAAzL,QAAA,CAAAyL,aAAA,CAAApN,MAAA;QACA;QACA,kBAAAmM,IAAA,CAAAxK,QAAA,CAAAyL,aAAA;UACAhT,YAAA;UACAuH,QAAA,CAAAvH,YAAA,GAAAA,YAAA;UACAuH,QAAA,CAAA9B,IAAA,GAAAzF,YAAA;UACAuH,QAAA,CAAAqL,QAAA,QAAAC,kBAAA,CAAA7S,YAAA;QACA;MACA;;MAEA;MACAuH,QAAA,CAAArH,eAAA,QAAAyS,oBAAA,CAAApL,QAAA,CAAArH,eAAA;MACAqH,QAAA,CAAAoI,OAAA,GAAApI,QAAA,CAAArH,eAAA;MAEA,OAAAqH,QAAA;IACA;IAEA;IACA8K,YAAA,WAAAA,aAAAlB,IAAA;MACA;MACA,6BAAAY,IAAA,CAAAZ,IAAA;IACA;IAEA;IACAmB,YAAA,WAAAA,aAAAnB,IAAA;MACA;MACA,sBAAAY,IAAA,CAAAZ,IAAA;IACA;IAEA;IACAoB,iBAAA,WAAAA,kBAAApB,IAAA;MACA;MACA,sBAAAY,IAAA,CAAAZ,IAAA;IACA;IAEA;IACAqB,gBAAA,WAAAA,iBAAArB,IAAA;MACA;MACA,sBAAAY,IAAA,CAAAZ,IAAA;IACA;IAEA;IACA0B,kBAAA,WAAAA,mBAAApN,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACA2N,iBAAA,WAAAA,kBAAAzD,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAA0D,gBAAA,GAAA1D,OAAA,CAAAzH,OAAA,mDAAAgK,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,SAAArB,KAAA;UAEA,IAAAqB,GAAA,CAAAlF,UAAA,eAAAkF,GAAA,CAAAlF,UAAA,gBAAAkF,GAAA,CAAAlF,UAAA;YACA,OAAA6D,KAAA;UACA;UAEA,IAAAuB,OAAA,8BAAAF,GAAA,CAAAlF,UAAA,QAAAkF,GAAA,SAAAA,GAAA;UACA,cAAAxN,MAAA,CAAAuN,MAAA,YAAAvN,MAAA,CAAA0N,OAAA,QAAA1N,MAAA,CAAAyN,KAAA;QACA;QAEA,OAAAH,gBAAA;MACA,SAAAxP,KAAA;QACA,OAAA8L,OAAA;MACA;IACA;IAEA;IACAb,0BAAA,WAAAA,2BAAAa,OAAA;MAAA,IAAA+D,OAAA;MACA,KAAA/D,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA;QACA,IAAA0D,gBAAA,GAAA1D;QACA;QAAA,CACAzH,OAAA,oDAAAgK,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,KAAAD,GAAA,CAAAlF,UAAA,aAAAkF,GAAA,CAAAlF,UAAA;YACA,IAAAoF,OAAA,GAAAC,OAAA,CAAAN,iBAAA,CAAAG,GAAA;YACA,cAAAxN,MAAA,CAAAuN,MAAA,YAAAvN,MAAA,CAAA0N,OAAA,QAAA1N,MAAA,CAAAyN,KAAA;UACA;UACA,OAAAtB,KAAA;QACA;QACA;QAAA,CACAhK,OAAA,sBACAA,OAAA;QACA;QAAA,CACAA,OAAA;QACA;QAAA,CACAA,OAAA,sBACAA,OAAA;QAEA,OAAAmL,gBAAA,CAAA3Q,IAAA;MACA,SAAAmB,KAAA;QACA,OAAA8L,OAAA;MACA;IACA;IAEA;IACAZ,uBAAA,WAAAA,wBAAAY,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA;MACA;MAEA;QACA,IAAAgE,MAAA;QACA,IAAAC,UAAA;QACA,IAAAC,uBAAA,GAAAlE,OAAA,CAAAzH,OAAA,2BAAAgK,KAAA;UACAyB,MAAA,CAAA1M,IAAA,CAAAiL,KAAA;UACA,gCAAAnM,MAAA,CAAA6N,UAAA;QACA;QAEA,IAAA5C,WAAA,GAAA6C,uBAAA,CACA3L,OAAA,uBACAA,OAAA,kBACAA,OAAA,qBACAA,OAAA,iBACAA,OAAA;QAEA,IAAA4L,YAAA,GAAA9C,WAAA;QACA2C,MAAA,CAAAzO,OAAA,WAAA6O,GAAA,EAAA7M,KAAA;UACA,IAAAkI,WAAA,0BAAArJ,MAAA,CAAAmB,KAAA;UACA,IAAA4M,YAAA,CAAA9M,QAAA,CAAAoI,WAAA;YACA0E,YAAA,GAAAA,YAAA,CAAA5L,OAAA,CAAAkH,WAAA,EAAA2E,GAAA;UACA;QACA;QAEA,OAAAD,YAAA,CAAApR,IAAA;MACA,SAAAmB,KAAA;QACA,OAAA8L,OAAA;MACA;IACA;IAEA;IACAuD,qBAAA,WAAAA,sBAAAjC,KAAA,EAAA+C,UAAA;MACA,IAAAjB,OAAA;MAEA,KAAA5C,KAAA,CAAA8D,OAAA,CAAAhD,KAAA,KAAA+C,UAAA,QAAAA,UAAA,IAAA/C,KAAA,CAAArL,MAAA;QACA;UAAAmN,OAAA,EAAAA;QAAA;MACA;MAEA;QACA,SAAAxB,CAAA,GAAAyC,UAAA,EAAAzC,CAAA,GAAAN,KAAA,CAAArL,MAAA,EAAA2L,CAAA;UACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;UAEA,KAAAJ,IAAA,WAAAA,IAAA;YACA;UACA;;UAEA;UACA,IAAA+C,WAAA,GAAA/C,IAAA,CAAAe,KAAA;UACA,IAAAgC,WAAA;YACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;YACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAAxR,IAAA;YAEA,IAAAyR,SAAA,IAAAE,aAAA;cACAtB,OAAA,CAAA9L,IAAA;gBACAkN,SAAA,EAAAA,SAAA;gBACAG,KAAA,EAAAH,SAAA;gBACAE,aAAA,EAAAA,aAAA;gBACA1E,OAAA,EAAA0E;cACA;YACA;UACA,gBAAA/B,YAAA,CAAAnB,IAAA,UAAAoB,iBAAA,CAAApB,IAAA,UAAAqB,gBAAA,CAAArB,IAAA;YACA;YACA;UACA;YACA;YACA;YACA,IAAAoD,oBAAA,GAAApD,IAAA,CAAAe,KAAA;YACA,IAAAqC,oBAAA;cACA;cACA,IAAAC,aAAA,GAAArD,IAAA,CAAAD,KAAA;cAAA,IAAAuD,SAAA,OAAAC,2BAAA,CAAA7P,OAAA,EACA2P,aAAA;gBAAAG,KAAA;cAAA;gBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAhJ,CAAA,IAAAjD,IAAA;kBAAA,IAAAqM,YAAA,GAAAF,KAAA,CAAAtF,KAAA;kBACA,KAAAwF,YAAA;kBAEA,IAAA3C,KAAA,GAAA2C,YAAA,CAAA3C,KAAA;kBACA,IAAAA,KAAA;oBACA,IAAAiC,UAAA,GAAAjC,KAAA,IAAAkC,WAAA;oBACA,IAAAC,cAAA,GAAAnC,KAAA,MAAAA,KAAA,IAAAxP,IAAA;oBAEA,IAAAyR,UAAA,IAAAE,cAAA;sBACAtB,OAAA,CAAA9L,IAAA;wBACAkN,SAAA,EAAAA,UAAA;wBACAG,KAAA,EAAAH,UAAA;wBACAE,aAAA,EAAAA,cAAA;wBACA1E,OAAA,EAAA0E;sBACA;oBACA;kBACA;gBACA;cAAA,SAAAS,GAAA;gBAAAL,SAAA,CAAAjG,CAAA,CAAAsG,GAAA;cAAA;gBAAAL,SAAA,CAAAM,CAAA;cAAA;YACA;UACA;QACA;MACA,SAAAlR,KAAA;QACA;MAAA;MAGA;QAAAkP,OAAA,EAAAA;MAAA;IACA;IAEA;IACAI,0BAAA,WAAAA,2BAAAlC,KAAA,EAAA1J,QAAA;MACA,SAAAgK,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAArL,MAAA,EAAA2L,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;QAEA;QACA,IAAAyD,WAAA,GAAA7D,IAAA,CAAAe,KAAA;QACA,IAAA8C,WAAA;UACAzN,QAAA,CAAAyL,aAAA,QAAAiC,gBAAA,CAAAD,WAAA,KAAAzN,QAAA,CAAAvH,YAAA;UACA;QACA;;QAEA;QACA,IAAAkV,gBAAA,GAAA/D,IAAA,CAAAe,KAAA;QACA,IAAAgD,gBAAA;UACA3N,QAAA,CAAAuL,WAAA,GAAAoC,gBAAA,IAAAxS,IAAA;UACA;QACA;;QAEA;QACA,IAAAyS,eAAA,GAAAhE,IAAA,CAAAe,KAAA;QACA,IAAAiD,eAAA;UACA,IAAAlV,UAAA,GAAAkV,eAAA;UACA;UACA,IAAAlV,UAAA;YACAA,UAAA;UACA;UACA;UACA,uBAAA+G,QAAA,CAAA/G,UAAA;YACAsH,QAAA,CAAAtH,UAAA,GAAAA,UAAA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA,KAAAsH,QAAA,CAAAyL,aAAA;QACAzL,QAAA,CAAAyL,aAAA,QAAAoC,gCAAA,CAAA7N,QAAA,CAAArH,eAAA,EAAAqH,QAAA,CAAAvH,YAAA;MACA;IACA;IAEA;IACAoV,gCAAA,WAAAA,iCAAAlV,eAAA,EAAAF,YAAA;MACA,KAAAE,eAAA,WAAAA,eAAA;QACA;MACA;MAEA;QACA;QACA,IAAAmV,QAAA,IACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA,CACA;QAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAA3P,MAAA,EAAA0P,GAAA;UAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;UACA,IAAAG,OAAA,GAAAvV,eAAA,CAAAgS,KAAA,CAAAsD,OAAA;UACA,IAAAC,OAAA,IAAAA,OAAA,CAAA7P,MAAA;YACA;YACA,IAAA8P,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAA7P,MAAA;YACA,IAAA+P,MAAA,GAAAD,SAAA,CAAAxN,OAAA,sBAAAxF,IAAA;YAEA,IAAAiT,MAAA;cACA,YAAAV,gBAAA,CAAAU,MAAA,EAAA3V,YAAA;YACA;UACA;QACA;MACA,SAAA6D,KAAA;QACA;MAAA;MAGA;IACA;IAEA;IACAoR,gBAAA,WAAAA,iBAAAW,UAAA,EAAA5V,YAAA;MACA,KAAA4V,UAAA,WAAAA,UAAA;QACA;MACA;MAEA;QACA,IAAAC,aAAA,GAAAD,UAAA,CAAAlT,IAAA;QAEA,KAAAmT,aAAA;UACA;QACA;QAEA,IAAA7V,YAAA;UACA;UACA,OAAA6V,aAAA;QACA;UACA;UACA,OAAAA,aAAA,CAAAzB,WAAA;QACA;MACA,SAAAvQ,KAAA;QACA,OAAA+R,UAAA;MACA;IACA;IAMA;IACAE,2BAAA,WAAAA,4BAAAvO,QAAA;MACA,KAAAA,QAAA,KAAAA,QAAA,CAAArH,eAAA;QACA;MACA;MAEA,IAAAyP,OAAA,GAAApI,QAAA,CAAArH,eAAA;;MAEA;MACA,SAAAY,mBAAA,SAAAA,mBAAA,CAAAkG,QAAA;QACA;QACA,IAAA+O,WAAA,QAAAC,uBAAA,CAAAzO,QAAA,CAAArH,eAAA,OAAAY,mBAAA;QACA,IAAAiV,WAAA;UACApG,OAAA,GAAAoG,WAAA;QACA;MACA;;MAEA;MACApG,OAAA,QAAAgD,oBAAA,CAAAhD,OAAA;MAEA,YAAAyD,iBAAA,CAAAzD,OAAA;IACA;IAEA;IACAsG,mBAAA,WAAAA,oBAAAxQ,IAAA;MACA,IAAAX,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAW,IAAA;IACA;IAEA;IACAkN,oBAAA,WAAAA,qBAAAhD,OAAA;MACA,KAAAA,OAAA,WAAAA,OAAA;QACA,OAAAA,OAAA;MACA;;MAEA;MACA,IAAAA,OAAA,CAAA3I,QAAA;QACA;QACA,OAAA2I,OAAA,CAAAzH,OAAA,wDACAA,OAAA;QAAA,CACAA,OAAA;MACA;QACA;QACA,OAAAyH,OAAA,CAAAzH,OAAA,0BAAAxF,IAAA;MACA;IACA;IAEA;IACAsT,uBAAA,WAAAA,wBAAAE,YAAA,EAAAH,WAAA;MACA,KAAAG,YAAA,KAAAH,WAAA;QACA,OAAAG,YAAA;MACA;MAEA;QACA;QACA,IAAAC,SAAA,GAAAD,YAAA,CAAAhO,OAAA,uBAAAxF,IAAA;;QAEA;QACA,IAAA0T,UAAA,GAAAL,WAAA,CAAA7D,KAAA;QAAA,IAAAmE,UAAA,OAAA3B,2BAAA,CAAA7P,OAAA,EAEAuR,UAAA;UAAAE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAzB,CAAA,MAAA0B,MAAA,GAAAD,UAAA,CAAA5K,CAAA,IAAAjD,IAAA;YAAA,IAAA+N,SAAA,GAAAD,MAAA,CAAAjH,KAAA;YACA,IAAAmH,aAAA,GAAAD,SAAA,CAAArO,OAAA,iBAAAxF,IAAA;YACA;YACA,IAAA+T,kBAAA,GAAAD,aAAA,CAAAtO,OAAA,0BAAAxF,IAAA;YACA,IAAA+T,kBAAA,CAAAzP,QAAA,CAAAmP,SAAA,CAAA/N,SAAA;cACA;cACA,YAAAuK,oBAAA,CAAA4D,SAAA;YACA;UACA;QAAA,SAAAzB,GAAA;UAAAuB,UAAA,CAAA7H,CAAA,CAAAsG,GAAA;QAAA;UAAAuB,UAAA,CAAAtB,CAAA;QAAA;QAEA,OAAAmB,YAAA;MACA,SAAArS,KAAA;QACA,OAAAqS,YAAA;MACA;IACA;IAGA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAA7W,WAAA,CAAAC,OAAA;MACA,KAAAiE,eAAA;IACA;IACA;IACA4S,WAAA,WAAAA,YAAA;MACA,KAAA9W,WAAA,CAAAG,YAAA;MACA,KAAAH,WAAA,CAAAI,UAAA;MACA,KAAAJ,WAAA,CAAAK,eAAA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAAiE,eAAA;IACA;EACA;AACA", "ignoreList": []}]}