{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBRdWVzdGlvbkNhcmQgZnJvbSAnLi9jb21wb25lbnRzL1F1ZXN0aW9uQ2FyZCcKaW1wb3J0IFF1ZXN0aW9uRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvUXVlc3Rpb25Gb3JtJwppbXBvcnQgQmF0Y2hJbXBvcnQgZnJvbSAnLi9jb21wb25lbnRzL0JhdGNoSW1wb3J0JwppbXBvcnQgeyBsaXN0UXVlc3Rpb24sIGRlbFF1ZXN0aW9uLCBnZXRRdWVzdGlvblN0YXRpc3RpY3MgfSBmcm9tICdAL2FwaS9iaXovcXVlc3Rpb24nCmltcG9ydCB7IGJhdGNoSW1wb3J0UXVlc3Rpb25zIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUXVlc3Rpb25CYW5rRGV0YWlsIiwKICBjb21wb25lbnRzOiB7CiAgICBRdWVzdGlvbkNhcmQsCiAgICBRdWVzdGlvbkZvcm0sCiAgICBCYXRjaEltcG9ydAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmimOW6k+S/oeaBrwogICAgICBiYW5rSWQ6IG51bGwsCiAgICAgIGJhbmtOYW1lOiAnJywKICAgICAgLy8g57uf6K6h5pWw5o2uCiAgICAgIHN0YXRpc3RpY3M6IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgbXVsdGlwbGVDaG9pY2U6IDAsCiAgICAgICAganVkZ21lbnQ6IDAKICAgICAgfSwKICAgICAgLy8g6aKY55uu5YiX6KGoCiAgICAgIHF1ZXN0aW9uTGlzdDogW10sCiAgICAgIC8vIOWIhumhteWPguaVsAogICAgICB0b3RhbDogMCwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rSWQ6IG51bGwsCiAgICAgICAgcXVlc3Rpb25UeXBlOiBudWxsLAogICAgICAgIGRpZmZpY3VsdHk6IG51bGwsCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOWxleW8gOeKtuaAgQogICAgICBleHBhbmRBbGw6IGZhbHNlLAogICAgICBleHBhbmRlZFF1ZXN0aW9uczogW10sCiAgICAgIC8vIOmAieaLqeeKtuaAgQogICAgICBzZWxlY3RlZFF1ZXN0aW9uczogW10sCiAgICAgIGlzQWxsU2VsZWN0ZWQ6IGZhbHNlLAogICAgICAvLyDooajljZXnm7jlhbMKICAgICAgcXVlc3Rpb25Gb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRRdWVzdGlvblR5cGU6ICdzaW5nbGUnLAogICAgICBjdXJyZW50UXVlc3Rpb25EYXRhOiBudWxsLAogICAgICAvLyDmibnph4/lr7zlhaUKICAgICAgaW1wb3J0RHJhd2VyVmlzaWJsZTogZmFsc2UsCiAgICAgIGJhdGNoSW1wb3J0VmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRJbXBvcnRNb2RlOiAnZXhjZWwnLAogICAgICAvLyDmlofmoaPlr7zlhaXmir3lsYkKICAgICAgZG9jdW1lbnRDb250ZW50OiAnJywKICAgICAgZG9jdW1lbnRIdG1sQ29udGVudDogJycsCiAgICAgIHBhcnNlZFF1ZXN0aW9uczogW10sCiAgICAgIHBhcnNlRXJyb3JzOiBbXSwKICAgICAgYWxsRXhwYW5kZWQ6IHRydWUsCiAgICAgIGlzU2V0dGluZ0Zyb21CYWNrZW5kOiBmYWxzZSwKICAgICAgZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcnVsZXNEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgYWN0aXZlUnVsZVRhYjogJ2V4YW1wbGVzJywKICAgICAgLy8g5LiK5Lyg5ZKM6Kej5p6Q54q25oCBCiAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgaXNQYXJzaW5nOiBmYWxzZSwKICAgICAgaW1wb3J0T3B0aW9uczogewogICAgICAgIHJldmVyc2U6IGZhbHNlLAogICAgICAgIGFsbG93RHVwbGljYXRlOiBmYWxzZQogICAgICB9LAogICAgICAvLyDmlofku7bkuIrkvKAKICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgJy9iaXovcXVlc3Rpb25CYW5rL3VwbG9hZERvY3VtZW50JywKICAgICAgdXBsb2FkSGVhZGVyczogewogICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIHRoaXMuJHN0b3JlLmdldHRlcnMudG9rZW4KICAgICAgfSwKICAgICAgdXBsb2FkRGF0YToge30sCiAgICAgIC8vIOWvjOaWh+acrOe8lui+keWZqAogICAgICByaWNoRWRpdG9yOiBudWxsLAogICAgICBlZGl0b3JJbml0aWFsaXplZDogZmFsc2UKICAgIH0KICB9LAoKICB3YXRjaDogewogICAgLy8g55uR5ZCs5paH5qGj5YaF5a655Y+Y5YyW77yM6Ieq5Yqo6Kej5p6QCiAgICBkb2N1bWVudENvbnRlbnQ6IHsKICAgICAgaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICAvLyDlpoLmnpzmmK/ku47lkI7nq6/orr7nva7lhoXlrrnvvIzkuI3op6blj5HliY3nq6/op6PmnpAKICAgICAgICBpZiAodGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCkgewogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKCgogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLnRyaW0oKSkgewogICAgICAgICAgdGhpcy5kZWJvdW5jZVBhcnNlRG9jdW1lbnQoKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogZmFsc2UKICAgIH0sCiAgICAvLyDnm5HlkKzmir3lsYnmiZPlvIDnirbmgIEKICAgIGltcG9ydERyYXdlclZpc2libGU6IHsKICAgICAgaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICBpZiAobmV3VmFsKSB7CiAgICAgICAgICAvLyDmir3lsYnmiZPlvIDml7bmuIXnqbrmiYDmnInlhoXlrrnlubbliJ3lp4vljJbnvJbovpHlmagKICAgICAgICAgIHRoaXMuY2xlYXJJbXBvcnRDb250ZW50KCkKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5pbml0UmljaEVkaXRvcigpCiAgICAgICAgICB9KQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDmir3lsYnlhbPpl63ml7bplIDmr4HnvJbovpHlmagKICAgICAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IpIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLmRlc3Ryb3koKQogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSBudWxsCiAgICAgICAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiBmYWxzZQogICAgfQogIH0sCgogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmluaXRQYWdlKCkKICAgIC8vIOWIm+W7uumYsuaKluWHveaVsAogICAgdGhpcy5kZWJvdW5jZVBhcnNlRG9jdW1lbnQgPSB0aGlzLmRlYm91bmNlKHRoaXMucGFyc2VEb2N1bWVudCwgMTAwMCkKICAgIC8vIOWIneWni+WMluS4iuS8oOaVsOaNrgogICAgdGhpcy51cGxvYWREYXRhID0gewogICAgICBiYW5rSWQ6IHRoaXMuYmFua0lkCiAgICB9CiAgICB0aGlzLnVwbG9hZEhlYWRlcnMgPSB7CiAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIHRoaXMuJHN0b3JlLmdldHRlcnMudG9rZW4KICAgIH0KICB9LAoKICBtb3VudGVkKCkgewogICAgLy8g57yW6L6R5Zmo5bCG5Zyo5oq95bGJ5omT5byA5pe25Yid5aeL5YyWCgogIH0sCgogIGJlZm9yZURlc3Ryb3koKSB7CgoKICAgIC8vIOmUgOavgeWvjOaWh+acrOe8lui+keWZqAogICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICB0aGlzLnJpY2hFZGl0b3IuZGVzdHJveSgpCiAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGwKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIneWni+WMlumhtemdogogICAgaW5pdFBhZ2UoKSB7CiAgICAgIGNvbnN0IHsgYmFua0lkLCBiYW5rTmFtZSB9ID0gdGhpcy4kcm91dGUucXVlcnkKICAgICAgaWYgKCFiYW5rSWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvLrlsJHpopjlupNJROWPguaVsCcpCiAgICAgICAgdGhpcy5nb0JhY2soKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuYmFua0lkID0gYmFua0lkCiAgICAgIHRoaXMuYmFua05hbWUgPSBiYW5rTmFtZSB8fCAn6aKY5bqT6K+m5oOFJwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJhbmtJZCA9IGJhbmtJZAogICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICB9LAogICAgLy8g6L+U5Zue6aKY5bqT5YiX6KGoCiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5iYWNrKCkKICAgIH0sCiAgICAvLyDojrflj5bpopjnm67liJfooagKICAgIGdldFF1ZXN0aW9uTGlzdCgpIHsKICAgICAgLy8g6L2s5o2i5p+l6K+i5Y+C5pWw5qC85byPCiAgICAgIGNvbnN0IHBhcmFtcyA9IHRoaXMuY29udmVydFF1ZXJ5UGFyYW1zKHRoaXMucXVlcnlQYXJhbXMpCiAgICAgIGxpc3RRdWVzdGlvbihwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucXVlc3Rpb25MaXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlumimOebruWIl+ihqOWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOi9rOaNouafpeivouWPguaVsOagvOW8jwogICAgY29udmVydFF1ZXJ5UGFyYW1zKHBhcmFtcykgewogICAgICBjb25zdCBjb252ZXJ0ZWRQYXJhbXMgPSB7IC4uLnBhcmFtcyB9CgogICAgICAvLyDovazmjaLpopjlnosKICAgICAgaWYgKGNvbnZlcnRlZFBhcmFtcy5xdWVzdGlvblR5cGUpIHsKICAgICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgICAgJ3NpbmdsZSc6IDEsCiAgICAgICAgICAnbXVsdGlwbGUnOiAyLAogICAgICAgICAgJ2p1ZGdtZW50JzogMwogICAgICAgIH0KICAgICAgICBjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlID0gdHlwZU1hcFtjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlXSB8fCBjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlCiAgICAgIH0KCiAgICAgIC8vIOi9rOaNoumavuW6pgogICAgICBpZiAoY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHkpIHsKICAgICAgICBjb25zdCBkaWZmaWN1bHR5TWFwID0gewogICAgICAgICAgJ+eugOWNlSc6IDEsCiAgICAgICAgICAn5Lit562JJzogMiwKICAgICAgICAgICflm7Dpmr4nOiAzCiAgICAgICAgfQogICAgICAgIGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5ID0gZGlmZmljdWx0eU1hcFtjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eV0gfHwgY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHkKICAgICAgfQoKICAgICAgLy8g5riF55CG56m65YC8CiAgICAgIE9iamVjdC5rZXlzKGNvbnZlcnRlZFBhcmFtcykuZm9yRWFjaChrZXkgPT4gewogICAgICAgIGlmIChjb252ZXJ0ZWRQYXJhbXNba2V5XSA9PT0gJycgfHwgY29udmVydGVkUGFyYW1zW2tleV0gPT09IG51bGwgfHwgY29udmVydGVkUGFyYW1zW2tleV0gPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgZGVsZXRlIGNvbnZlcnRlZFBhcmFtc1trZXldCiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIGNvbnZlcnRlZFBhcmFtcwogICAgfSwKICAgIC8vIOiOt+WPlue7n+iuoeaVsOaNrgogICAgZ2V0U3RhdGlzdGljcygpIHsKICAgICAgZ2V0UXVlc3Rpb25TdGF0aXN0aWNzKHRoaXMuYmFua0lkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnN0YXRpc3RpY3MgPSByZXNwb25zZS5kYXRhCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKCiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uCiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gewogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICAgIGp1ZGdtZW50OiAwCiAgICAgICAgfQogICAgICB9KQogICAgfSwKCgogICAgLy8g5aSE55CG5om56YeP5a+86aKY5oyJ6ZKu54K55Ye7CiAgICBoYW5kbGVCYXRjaEltcG9ydENsaWNrKCkgewogICAgICB0aGlzLmltcG9ydERyYXdlclZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5re75Yqg6aKY55uuCiAgICBoYW5kbGVBZGRRdWVzdGlvbih0eXBlKSB7CiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHR5cGUKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gbnVsbAogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlCiAgICB9LAogICAgLy8g5YiH5o2i5bGV5byA54q25oCBCiAgICB0b2dnbGVFeHBhbmRBbGwoKSB7CiAgICAgIHRoaXMuZXhwYW5kQWxsID0gIXRoaXMuZXhwYW5kQWxsCiAgICAgIGlmICghdGhpcy5leHBhbmRBbGwpIHsKICAgICAgICB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zID0gW10KICAgICAgfQogICAgfSwKCgoKICAgIC8vIOWvvOWHuumimOebrgogICAgaGFuZGxlRXhwb3J0UXVlc3Rpb25zKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWvvOWHuueahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKGDmraPlnKjlr7zlh7ogJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuLi4uYCkKICAgICAgLy8gVE9ETzog5a6e546w5a+85Ye65Yqf6IO9CiAgICB9LAoKICAgIC8vIOWIh+aNouWFqOmAiS/lhajkuI3pgIkKICAgIGhhbmRsZVRvZ2dsZVNlbGVjdEFsbCgpIHsKICAgICAgdGhpcy5pc0FsbFNlbGVjdGVkID0gIXRoaXMuaXNBbGxTZWxlY3RlZAogICAgICBpZiAodGhpcy5pc0FsbFNlbGVjdGVkKSB7CiAgICAgICAgLy8g5YWo6YCJCiAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucyA9IHRoaXMucXVlc3Rpb25MaXN0Lm1hcChxID0+IHEucXVlc3Rpb25JZCkKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3sumAieaLqSAke3RoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWFqOS4jemAiQogICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSBbXQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5bey5Y+W5raI6YCJ5oup5omA5pyJ6aKY55uuJykKICAgICAgfQogICAgfSwKCgoKICAgIC8vIOaJuemHj+WIoOmZpAogICAgaGFuZGxlQmF0Y2hEZWxldGUoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5Yig6Zmk55qE6aKY55uuJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5Yig6Zmk6YCJ5Lit55qEICR7dGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebruWQl++8n2AsICfmibnph4/liKDpmaQnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBjb25zdCBkZWxldGVQcm9taXNlcyA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubWFwKHF1ZXN0aW9uSWQgPT4KICAgICAgICAgIGRlbFF1ZXN0aW9uKHF1ZXN0aW9uSWQpCiAgICAgICAgKQoKICAgICAgICBQcm9taXNlLmFsbChkZWxldGVQcm9taXNlcykudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WIoOmZpCAke3RoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKQogICAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLmlzQWxsU2VsZWN0ZWQgPSBmYWxzZQogICAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmibnph4/liKDpmaTlpLHotKUnKQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suWPlua2iOWIoOmZpCcpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOmimOebrumAieaLqeeKtuaAgeWPmOWMlgogICAgaGFuZGxlUXVlc3Rpb25TZWxlY3QocXVlc3Rpb25JZCwgc2VsZWN0ZWQpIHsKICAgICAgaWYgKHNlbGVjdGVkKSB7CiAgICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmluY2x1ZGVzKHF1ZXN0aW9uSWQpKSB7CiAgICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLnB1c2gocXVlc3Rpb25JZCkKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmluZGV4T2YocXVlc3Rpb25JZCkKICAgICAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmm7TmlrDlhajpgInnirbmgIEKICAgICAgdGhpcy5pc0FsbFNlbGVjdGVkID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IHRoaXMucXVlc3Rpb25MaXN0Lmxlbmd0aAogICAgfSwKICAgIC8vIOWIh+aNouWNleS4qumimOebruWxleW8gOeKtuaAgQogICAgaGFuZGxlVG9nZ2xlRXhwYW5kKHF1ZXN0aW9uSWQpIHsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zLmluZGV4T2YocXVlc3Rpb25JZCkKICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICAvLyDmlLbotbfpopjnm64KICAgICAgICB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zLnNwbGljZShpbmRleCwgMSkKICAgICAgICAvLyDlpoLmnpzlvZPliY3mmK8i5bGV5byA5omA5pyJIueKtuaAge+8jOWImeWPlua2iCLlsZXlvIDmiYDmnIki54q25oCBCiAgICAgICAgaWYgKHRoaXMuZXhwYW5kQWxsKSB7CiAgICAgICAgICB0aGlzLmV4cGFuZEFsbCA9IGZhbHNlCiAgICAgICAgICAvLyDlsIblhbbku5bpopjnm67mt7vliqDliLBleHBhbmRlZFF1ZXN0aW9uc+aVsOe7hOS4re+8jOmZpOS6huW9k+WJjeimgeaUtui1t+eahOmimOebrgogICAgICAgICAgdGhpcy5xdWVzdGlvbkxpc3QuZm9yRWFjaChxdWVzdGlvbiA9PiB7CiAgICAgICAgICAgIGlmIChxdWVzdGlvbi5xdWVzdGlvbklkICE9PSBxdWVzdGlvbklkICYmICF0aGlzLmV4cGFuZGVkUXVlc3Rpb25zLmluY2x1ZGVzKHF1ZXN0aW9uLnF1ZXN0aW9uSWQpKSB7CiAgICAgICAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5wdXNoKHF1ZXN0aW9uLnF1ZXN0aW9uSWQpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWxleW8gOmimOebrgogICAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbklkKQogICAgICB9CiAgICB9LAogICAgLy8g57yW6L6R6aKY55uuCiAgICBoYW5kbGVFZGl0UXVlc3Rpb24ocXVlc3Rpb24pIHsKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gcXVlc3Rpb24KICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlID0gcXVlc3Rpb24ucXVlc3Rpb25UeXBlCiAgICAgIHRoaXMucXVlc3Rpb25Gb3JtVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICAvLyDlpI3liLbpopjnm64KICAgIGhhbmRsZUNvcHlRdWVzdGlvbihxdWVzdGlvbikgewogICAgICAvLyDliJvlu7rlpI3liLbnmoTpopjnm67mlbDmja7vvIjnp7vpmaRJROebuOWFs+Wtl+aute+8iQogICAgICBjb25zdCBjb3BpZWRRdWVzdGlvbiA9IHsKICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICBxdWVzdGlvbklkOiBudWxsLCAgLy8g5riF6ZmkSUTvvIzooajnpLrmlrDlop4KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsCiAgICAgICAgY3JlYXRlQnk6IG51bGwsCiAgICAgICAgdXBkYXRlQnk6IG51bGwKICAgICAgfQoKICAgICAgLy8g6K6+572u5Li657yW6L6R5qih5byP5bm25omT5byA6KGo5Y2VCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uRGF0YSA9IGNvcGllZFF1ZXN0aW9uCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHRoaXMuY29udmVydFF1ZXN0aW9uVHlwZVRvU3RyaW5nKHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvLyDpopjlnovmlbDlrZfovazlrZfnrKbkuLLvvIjnlKjkuo7lpI3liLblip/og73vvIkKICAgIGNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgMTogJ3NpbmdsZScsCiAgICAgICAgMjogJ211bHRpcGxlJywKICAgICAgICAzOiAnanVkZ21lbnQnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgdHlwZQogICAgfSwKICAgIC8vIOWIoOmZpOmimOebrgogICAgaGFuZGxlRGVsZXRlUXVlc3Rpb24ocXVlc3Rpb24pIHsKICAgICAgY29uc3QgcXVlc3Rpb25Db250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50LnJlcGxhY2UoLzxbXj5dKj4vZywgJycpCiAgICAgIGNvbnN0IGRpc3BsYXlDb250ZW50ID0gcXVlc3Rpb25Db250ZW50Lmxlbmd0aCA+IDUwID8gcXVlc3Rpb25Db250ZW50LnN1YnN0cmluZygwLCA1MCkgKyAnLi4uJyA6IHF1ZXN0aW9uQ29udGVudAogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTliKDpmaTpopjnm64iJHtkaXNwbGF5Q29udGVudH0i5ZCX77yfYCwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGRlbFF1ZXN0aW9uKHF1ZXN0aW9uLnF1ZXN0aW9uSWQpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk6aKY55uu5aSx6LSlJykKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8vIOmimOebruihqOWNleaIkOWKn+WbnuiwgwogICAgaGFuZGxlUXVlc3Rpb25Gb3JtU3VjY2VzcygpIHsKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgfSwKICAgIC8vIOaJuemHj+WvvOWFpeaIkOWKn+WbnuiwgwogICAgaGFuZGxlQmF0Y2hJbXBvcnRTdWNjZXNzKCkgewogICAgICB0aGlzLmJhdGNoSW1wb3J0VmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuaW1wb3J0RHJhd2VyVmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgIH0sCgoKCiAgICAvLyDmir3lsYnlhbPpl63liY3lpITnkIYKICAgIGhhbmRsZURyYXdlckNsb3NlKGRvbmUpIHsKICAgICAgZG9uZSgpCiAgICB9LAoKICAgIC8vIOa4heepuuWvvOWFpeWGheWuuQogICAgY2xlYXJJbXBvcnRDb250ZW50KCkgewogICAgICAvLyDmuIXnqbrmlofmoaPlhoXlrrkKICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSAnJwogICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSAnJwoKICAgICAgLy8g5riF56m66Kej5p6Q57uT5p6cCiAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCgogICAgICAvLyDph43nva7op6PmnpDnirbmgIEKICAgICAgdGhpcy5hbGxFeHBhbmRlZCA9IHRydWUKICAgICAgdGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCA9IGZhbHNlCgogICAgICAvLyDph43nva7kuIrkvKDnirbmgIEKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKCiAgICAgIC8vIOmHjee9ruWvvOWFpemAiemhuQogICAgICB0aGlzLmltcG9ydE9wdGlvbnMgPSB7CiAgICAgICAgcmV2ZXJzZTogZmFsc2UsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0KICAgIH0sCgogICAgLy8g5pi+56S65paH5qGj5a+85YWl5a+56K+d5qGGCiAgICBzaG93RG9jdW1lbnRJbXBvcnREaWFsb2coKSB7CiAgICAgIC8vIOa4hemZpOS4iuS4gOasoeeahOS4iuS8oOeKtuaAgeWSjOWGheWuuQogICAgICB0aGlzLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQoKICAgICAgLy8g5riF6Zmk5LiK5Lyg57uE5Lu255qE5paH5Lu25YiX6KGoCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBjb25zdCB1cGxvYWRDb21wb25lbnQgPSB0aGlzLiRyZWZzLmRvY3VtZW50VXBsb2FkCiAgICAgICAgaWYgKHVwbG9hZENvbXBvbmVudCkgewogICAgICAgICAgdXBsb2FkQ29tcG9uZW50LmNsZWFyRmlsZXMoKQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHRoaXMuZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlID0gdHJ1ZQoKICAgIH0sCgogICAgLy8g5pi+56S66KeE6IyD5a+56K+d5qGGCiAgICBzaG93UnVsZXNEaWFsb2coKSB7CiAgICAgIHRoaXMuYWN0aXZlUnVsZVRhYiA9ICdleGFtcGxlcycgLy8g6buY6K6k5pi+56S66IyD5L6L5qCH562+6aG1CiAgICAgIHRoaXMucnVsZXNEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvLyDlsIbojIPkvovlpI3liLbliLDnvJbovpHljLogLSDlj6rkv53nlZnliY0z6aKY77ya5Y2V6YCJ44CB5aSa6YCJ44CB5Yik5patCiAgICBjb3B5RXhhbXBsZVRvRWRpdG9yKCkgewogICAgICAvLyDkvb/nlKjovpPlhaXojIPkvovmoIfnrb7pobXph4znmoTliY0z6aKY5YaF5a6577yM6L2s5o2i5Li6SFRNTOagvOW8jwogICAgICBjb25zdCBodG1sVGVtcGxhdGUgPSBgCjxwPjEu77yIICDvvInmmK/miJHlm73mnIDml6nnmoTor5fmrYzmgLvpm4bvvIzlj4jnp7DkvZwi6K+X5LiJ55m+IuOAgjwvcD4KPHA+QS7jgIrlt6bkvKDjgIs8L3A+CjxwPkIu44CK56a76aqa44CLPC9wPgo8cD5DLuOAiuWdm+e7j+OAizwvcD4KPHA+RC7jgIror5fnu4/jgIs8L3A+CjxwPuetlOahiO+8mkQ8L3A+CjxwPuino+aekO+8muivl+e7j+aYr+aIkeWbveacgOaXqeeahOivl+atjOaAu+mbhuOAgjwvcD4KPHA+6Zq+5bqm77ya5Lit562JPC9wPgo8cD48YnI+PC9wPgoKPHA+Mi7kuK3ljY7kurrmsJHlhbHlkozlm73nmoTmiJDnq4vvvIzmoIflv5fnnYDvvIgg77yJ44CCPC9wPgo8cD5BLuS4reWbveaWsOawkeS4u+S4u+S5iemdqeWRveWPluW+l+S6huWfuuacrOiDnOWIqTwvcD4KPHA+Qi7kuK3lm73njrDku6Plj7LnmoTlvIDlp4s8L3A+CjxwPkMu5Y2K5q6W5rCR5Zyw5Y2K5bCB5bu656S+5Lya55qE57uT5p2fPC9wPgo8cD5ELuS4reWbvei/m+WFpeekvuS8muS4u+S5ieekvuS8mjwvcD4KPHA+562U5qGI77yaQUJDPC9wPgo8cD7op6PmnpDvvJrmlrDkuK3lm73nmoTmiJDnq4vvvIzmoIflv5fnnYDmiJHlm73mlrDmsJHkuLvkuLvkuYnpnanlkb3pmLbmrrXnmoTln7rmnKznu5PmnZ/lkoznpL7kvJrkuLvkuYnpnanlkb3pmLbmrrXnmoTlvIDlp4vjgII8L3A+CjxwPjxicj48L3A+Cgo8cD4zLuWFg+adguWJp+eahOWbm+Wkp+aCsuWJp+aYr++8muWFs+axieWNv+eahOOAiueqpuWopeWGpOOAi++8jOmprOiHtOi/nOeahOOAiuaxieWuq+eni+OAi++8jOeZveactOeahOOAiuaip+ahkOmbqOOAi+WSjOmDkeWFieellueahOOAiui1teawj+WtpOWEv+OAi+OAgjwvcD4KPHA+562U5qGI77ya6ZSZ6K+vPC9wPgo8cD7op6PmnpDvvJrlhYPmnYLliafjgIrotbXmsI/lraTlhL/jgIvlhajlkI3jgIrlhqTmiqXlhqTotbXmsI/lraTlhL/jgIvvvIzkuLrnuqrlkJvnpaXmiYDkvZzjgII8L3A+CiAgICAgIGAudHJpbSgpCgogICAgICAvLyDnm7TmjqXorr7nva7liLDlr4zmlofmnKznvJbovpHlmagKICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKQoKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpznvJbovpHlmajmnKrliJ3lp4vljJbvvIznrYnlvoXliJ3lp4vljJblkI7lho3orr7nva4KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKQoKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CgogICAgICAvLyDlhbPpl63lr7nor53moYYKICAgICAgdGhpcy5ydWxlc0RpYWxvZ1Zpc2libGUgPSBmYWxzZQoKICAgICAgLy8g5o+Q56S655So5oi3CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6L6T5YWl6IyD5L6L5bey5aGr5YWF5Yiw57yW6L6R5Yy677yM5Y+z5L6n5bCG6Ieq5Yqo6Kej5p6QJykKCgogICAgfSwKCgoKICAgIC8vIOS4i+i9vVdvcmTmqKHmnb8KICAgIGRvd25sb2FkV29yZFRlbXBsYXRlKCkgewogICAgICB0aGlzLmRvd25sb2FkKCdiaXovcXVlc3Rpb25CYW5rL2Rvd25sb2FkV29yZFRlbXBsYXRlJywge30sIGDpopjnm67lr7zlhaVXb3Jk5qih5p2/LmRvY3hgKQogICAgfSwKCiAgICAvLyDkuIrkvKDliY3mo4Dmn6UKICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7CgoKICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcgfHwKICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fAogICAgICAgICAgICAgICAgICAgICAgICAgZmlsZS5uYW1lLmVuZHNXaXRoKCcuZG9jeCcpIHx8IGZpbGUubmFtZS5lbmRzV2l0aCgnLnhsc3gnKQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMAoKICAgICAgaWYgKCFpc1ZhbGlkVHlwZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oCAuZG9jeCDmiJYgLnhsc3gg5qC85byP55qE5paH5Lu2IScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgLy8g5pu05paw5LiK5Lyg5pWw5o2uCiAgICAgIHRoaXMudXBsb2FkRGF0YS5iYW5rSWQgPSB0aGlzLmJhbmtJZAoKICAgICAgLy8g6K6+572u5LiK5Lyg54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKCgoKICAgICAgcmV0dXJuIHRydWUKICAgIH0sCgogICAgLy8g5LiK5Lyg5oiQ5YqfCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlKSB7CgoKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIC8vIOS4iuS8oOWujOaIkO+8jOW8gOWni+ino+aekAogICAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuaXNQYXJzaW5nID0gdHJ1ZQoKCgogICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOino+aekOe7k+aenO+8jOehruS/neW5suWHgOeahOW8gOWniwogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KCiAgICAgICAgLy8g5bu26L+f5YWz6Zet5a+56K+d5qGG77yM6K6p55So5oi355yL5Yiw6Kej5p6Q5Yqo55S7CiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICB0aGlzLmRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCiAgICAgICAgfSwgMTUwMCkKCiAgICAgICAgLy8g6K6+572u5qCH5b+X5L2N77yM6YG/5YWN6Kem5Y+R5YmN56uv6YeN5paw6Kej5p6QCiAgICAgICAgdGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCA9IHRydWUKCiAgICAgICAgLy8g5bCG6Kej5p6Q57uT5p6c5pi+56S65Zyo5Y+z5L6nCiAgICAgICAgaWYgKHJlc3BvbnNlLnF1ZXN0aW9ucyAmJiByZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSByZXNwb25zZS5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAgIC4uLnF1ZXN0aW9uLAogICAgICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlICAvLyDpu5jorqTlsZXlvIAKICAgICAgICAgIH0pKQogICAgICAgICAgLy8g6YeN572u5YWo6YOo5bGV5byA54q25oCBCiAgICAgICAgICB0aGlzLmFsbEV4cGFuZGVkID0gdHJ1ZQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmVycm9ycyB8fCBbXQoKICAgICAgICAgIC8vIOaYvuekuuivpue7hueahOino+aekOe7k+aenAogICAgICAgICAgY29uc3QgZXJyb3JDb3VudCA9IHJlc3BvbnNlLmVycm9ycyA/IHJlc3BvbnNlLmVycm9ycy5sZW5ndGggOiAwCiAgICAgICAgICBpZiAoZXJyb3JDb3VudCA+IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/op6PmnpDlh7ogJHtyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67vvIzmnIkgJHtlcnJvckNvdW50fSDkuKrplJnor6/miJborablkYpgKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/op6PmnpDlh7ogJHtyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKQogICAgICAgICAgfQoKCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+acquino+aekOWHuuS7u+S9lemimOebru+8jOivt+ajgOafpeaWh+S7tuagvOW8jycpCiAgICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcmVzcG9uc2UuZXJyb3JzIHx8IFsn5pyq6IO96Kej5p6Q5Ye66aKY55uu5YaF5a65J10KCgogICAgICAgIH0KCiAgICAgICAgLy8g5bCG5Y6f5aeL5YaF5a655aGr5YWF5Yiw5a+M5paH5pys57yW6L6R5Zmo5LitCiAgICAgICAgaWYgKHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCkgewogICAgICAgICAgdGhpcy5zZXRFZGl0b3JDb250ZW50KHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCkKICAgICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gcmVzcG9uc2Uub3JpZ2luYWxDb250ZW50CiAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSByZXNwb25zZS5vcmlnaW5hbENvbnRlbnQgLy8g5Yid5aeL5YyWSFRNTOWGheWuuQoKICAgICAgICB9CgogICAgICAgIC8vIOW7tui/n+mHjee9ruagh+W/l+S9je+8jOehruS/neaJgOacieW8guatpeaTjeS9nOWujOaIkAogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgdGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCA9IGZhbHNlCiAgICAgICAgfSwgMjAwMCkKICAgICAgfSBlbHNlIHsKCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICAgICAgLy8g6YeN572u54q25oCBCiAgICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAoKICAgIC8vIOS4iuS8oOWksei0pQogICAgaGFuZGxlVXBsb2FkRXJyb3IoZXJyb3IsIGZpbGUpIHsKCiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pe+8jOivt+ajgOafpee9kee7nOi/nuaOpeaIluiBlOezu+euoeeQhuWRmCcpCgogICAgICAvLyDph43nva7nirbmgIEKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKICAgIH0sCgoKCiAgICAvLyDliIfmjaLpopjnm67lsZXlvIAv5pS26LW3CiAgICB0b2dnbGVRdWVzdGlvbihpbmRleCkgewogICAgICBjb25zdCBxdWVzdGlvbiA9IHRoaXMucGFyc2VkUXVlc3Rpb25zW2luZGV4XQogICAgICB0aGlzLiRzZXQocXVlc3Rpb24sICdjb2xsYXBzZWQnLCAhcXVlc3Rpb24uY29sbGFwc2VkKQogICAgfSwKCiAgICAvLyDlhajpg6jlsZXlvIAv5pS26LW3CiAgICB0b2dnbGVBbGxRdWVzdGlvbnMoKSB7CiAgICAgIHRoaXMuYWxsRXhwYW5kZWQgPSAhdGhpcy5hbGxFeHBhbmRlZAogICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucy5mb3JFYWNoKHF1ZXN0aW9uID0+IHsKICAgICAgICB0aGlzLiRzZXQocXVlc3Rpb24sICdjb2xsYXBzZWQnLCAhdGhpcy5hbGxFeHBhbmRlZCkKICAgICAgfSkKCiAgICB9LAoKICAgIC8vIOehruiupOWvvOWFpQogICAgY29uZmlybUltcG9ydCgpIHsKICAgICAgaWYgKHRoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5Y+v5a+85YWl55qE6aKY55uuJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5a+85YWlICR7dGhpcy5wYXJzZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67lkJfvvJ9gLCAn56Gu6K6k5a+85YWlJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnaW5mbycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRRdWVzdGlvbnMoKQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkKICAgIH0sCgogICAgLy8g5a+85YWl6aKY55uuCiAgICBhc3luYyBpbXBvcnRRdWVzdGlvbnMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aSE55CG5a+85YWl6YCJ6aG5CiAgICAgICAgbGV0IHF1ZXN0aW9uc1RvSW1wb3J0ID0gWy4uLnRoaXMucGFyc2VkUXVlc3Rpb25zXQoKICAgICAgICBpZiAodGhpcy5pbXBvcnRPcHRpb25zLnJldmVyc2UpIHsKICAgICAgICAgIHF1ZXN0aW9uc1RvSW1wb3J0LnJldmVyc2UoKQogICAgICAgIH0KCiAgICAgICAgLy8g6LCD55So5a6e6ZmF55qE5a+85YWlQVBJCiAgICAgICAgY29uc3QgaW1wb3J0RGF0YSA9IHsKICAgICAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQsCiAgICAgICAgICBxdWVzdGlvbnM6IHF1ZXN0aW9uc1RvSW1wb3J0LAogICAgICAgICAgYWxsb3dEdXBsaWNhdGU6IHRoaXMuaW1wb3J0T3B0aW9ucy5hbGxvd0R1cGxpY2F0ZQogICAgICAgIH0KCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBiYXRjaEltcG9ydFF1ZXN0aW9ucyhpbXBvcnREYXRhKQoKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WvvOWFpSAke3F1ZXN0aW9uc1RvSW1wb3J0Lmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5a+85YWl5aSx6LSlJykKICAgICAgICB9CiAgICAgICAgdGhpcy5pbXBvcnREcmF3ZXJWaXNpYmxlID0gZmFsc2UKICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9ICcnCiAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gJycKICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCgoKCiAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CgogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5Yid5aeL5YyW5a+M5paH5pys57yW6L6R5ZmoCiAgICBpbml0UmljaEVkaXRvcigpIHsKICAgICAgaWYgKHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5qOA5p+lQ0tFZGl0b3LmmK/lkKblj6/nlKgKICAgICAgaWYgKCF3aW5kb3cuQ0tFRElUT1IpIHsKCiAgICAgICAgdGhpcy5mYWxsYmFja1RvVGV4dGFyZWEoKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0cnkgewogICAgICAgIC8vIOWmguaenOe8lui+keWZqOW3suWtmOWcqO+8jOWFiOmUgOavgQogICAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IpIHsKICAgICAgICAgIHRoaXMucmljaEVkaXRvci5kZXN0cm95KCkKICAgICAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGwKICAgICAgICB9CgogICAgICAgIC8vIOehruS/neWuueWZqOWtmOWcqAogICAgICAgIGNvbnN0IGVkaXRvckNvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdyaWNoLWVkaXRvcicpCiAgICAgICAgaWYgKCFlZGl0b3JDb250YWluZXIpIHsKCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIC8vIOWIm+W7unRleHRhcmVh5YWD57SgCiAgICAgICAgZWRpdG9yQ29udGFpbmVyLmlubmVySFRNTCA9ICc8dGV4dGFyZWEgaWQ9InJpY2gtZWRpdG9yLXRleHRhcmVhIiBuYW1lPSJyaWNoLWVkaXRvci10ZXh0YXJlYSI+PC90ZXh0YXJlYT4nCgogICAgICAgIC8vIOetieW+hURPTeabtOaWsOWQjuWIm+W7uue8lui+keWZqAogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIC8vIOajgOafpUNLRWRpdG9y5piv5ZCm5Y+v55SoCiAgICAgICAgICBpZiAoIXdpbmRvdy5DS0VESVRPUiB8fCAhd2luZG93LkNLRURJVE9SLnJlcGxhY2UpIHsKCiAgICAgICAgICAgIHRoaXMuc2hvd0ZhbGxiYWNrRWRpdG9yID0gdHJ1ZQogICAgICAgICAgICByZXR1cm4KICAgICAgICAgIH0KCiAgICAgICAgICB0cnkgewogICAgICAgICAgICAvLyDlhYjlsJ3or5XlrozmlbTphY3nva4KICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gd2luZG93LkNLRURJVE9SLnJlcGxhY2UoJ3JpY2gtZWRpdG9yLXRleHRhcmVhJywgewogICAgICAgICAgICAgIGhlaWdodDogJ2NhbGMoMTAwdmggLSAyMDBweCknLCAvLyDlhajlsY/pq5jluqblh4/ljrvlpLTpg6jlkozlhbbku5blhYPntKDnmoTpq5jluqYKICAgICAgICAgICAgICB0b29sYmFyOiBbCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdzdHlsZXMnLCBpdGVtczogWydGb250U2l6ZSddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdiYXNpY3N0eWxlcycsIGl0ZW1zOiBbJ0JvbGQnLCAnSXRhbGljJywgJ1VuZGVybGluZScsICdTdHJpa2UnLCAnU3VwZXJzY3JpcHQnLCAnU3Vic2NyaXB0JywgJy0nLCAnUmVtb3ZlRm9ybWF0J10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2NsaXBib2FyZCcsIGl0ZW1zOiBbJ0N1dCcsICdDb3B5JywgJ1Bhc3RlJywgJ1Bhc3RlVGV4dCddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdjb2xvcnMnLCBpdGVtczogWydUZXh0Q29sb3InLCAnQkdDb2xvciddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdwYXJhZ3JhcGgnLCBpdGVtczogWydKdXN0aWZ5TGVmdCcsICdKdXN0aWZ5Q2VudGVyJywgJ0p1c3RpZnlSaWdodCcsICdKdXN0aWZ5QmxvY2snXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnZWRpdGluZycsIGl0ZW1zOiBbJ1VuZG8nLCAnUmVkbyddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdsaW5rcycsIGl0ZW1zOiBbJ0xpbmsnLCAnVW5saW5rJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2luc2VydCcsIGl0ZW1zOiBbJ0ltYWdlJywgJ1NwZWNpYWxDaGFyJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ3Rvb2xzJywgaXRlbXM6IFsnTWF4aW1pemUnXSB9CiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICByZW1vdmVCdXR0b25zOiAnJywKICAgICAgICAgICAgICBsYW5ndWFnZTogJ3poLWNuJywKICAgICAgICAgICAgICByZW1vdmVQbHVnaW5zOiAnZWxlbWVudHNwYXRoJywKICAgICAgICAgICAgICByZXNpemVfZW5hYmxlZDogZmFsc2UsCiAgICAgICAgICAgICAgZXh0cmFQbHVnaW5zOiAnZm9udCxjb2xvcmJ1dHRvbixqdXN0aWZ5LHNwZWNpYWxjaGFyLGltYWdlJywKICAgICAgICAgICAgICBhbGxvd2VkQ29udGVudDogdHJ1ZSwKICAgICAgICAgICAgICAvLyDlrZfkvZPlpKflsI/phY3nva4KICAgICAgICAgICAgICBmb250U2l6ZV9zaXplczogJzEyLzEycHg7MTQvMTRweDsxNi8xNnB4OzE4LzE4cHg7MjAvMjBweDsyMi8yMnB4OzI0LzI0cHg7MjYvMjZweDsyOC8yOHB4OzM2LzM2cHg7NDgvNDhweDs3Mi83MnB4JywKICAgICAgICAgICAgICBmb250U2l6ZV9kZWZhdWx0TGFiZWw6ICcxNHB4JywKICAgICAgICAgICAgICAvLyDpopzoibLphY3nva4KICAgICAgICAgICAgICBjb2xvckJ1dHRvbl9lbmFibGVNb3JlOiB0cnVlLAogICAgICAgICAgICAgIGNvbG9yQnV0dG9uX2NvbG9yczogJ0NGNUQ0RSw0NTQ1NDUsRkZGLENDQyxEREQsQ0NFQUVFLDY2QUIxNicsCiAgICAgICAgICAgICAgLy8g5Zu+5YOP5LiK5Lyg6YWN572uIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5qCH5YeG6YWN572uCiAgICAgICAgICAgICAgZmlsZWJyb3dzZXJVcGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2NvbW1vbi91cGxvYWRJbWFnZScsCiAgICAgICAgICAgICAgaW1hZ2VfcHJldmlld1RleHQ6ICcgJywKICAgICAgICAgICAgICAvLyDorr7nva7ln7rnoYDot6/lvoTvvIzorqnnm7jlr7not6/lvoTog73mraPnoa7op6PmnpDliLDlkI7nq6/mnI3liqHlmagKICAgICAgICAgICAgICBiYXNlSHJlZjogJ2h0dHA6Ly9sb2NhbGhvc3Q6ODgwMi8nLAogICAgICAgICAgICAgIC8vIOWbvuWDj+aPkuWFpemFjee9rgogICAgICAgICAgICAgIGltYWdlX3ByZXZpZXdUZXh0OiAn6aKE6KeI5Yy65Z+fJywKICAgICAgICAgICAgICBpbWFnZV9yZW1vdmVMaW5rQnlFbXB0eVVSTDogdHJ1ZSwKICAgICAgICAgICAgICAvLyDpmpDol4/kuI3pnIDopoHnmoTmoIfnrb7pobXvvIzlj6rkv53nlZnkuIrkvKDlkozlm77lg4/kv6Hmga8KICAgICAgICAgICAgICByZW1vdmVEaWFsb2dUYWJzOiAnaW1hZ2U6TGluaztpbWFnZTphZHZhbmNlZCcsCiAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgIGluc3RhbmNlUmVhZHk6IGZ1bmN0aW9uKGV2dCkgewogICAgICAgICAgICAgICAgICBjb25zdCBlZGl0b3IgPSBldnQuZWRpdG9yCiAgICAgICAgICAgICAgICAgIGVkaXRvci5vbignZGlhbG9nU2hvdycsIGZ1bmN0aW9uKGV2dCkgewogICAgICAgICAgICAgICAgICAgIGNvbnN0IGRpYWxvZyA9IGV2dC5kYXRhCiAgICAgICAgICAgICAgICAgICAgaWYgKGRpYWxvZy5nZXROYW1lKCkgPT09ICdpbWFnZScpIHsKICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGVja0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cmxGaWVsZCA9IGRpYWxvZy5nZXRDb250ZW50RWxlbWVudCgnaW5mbycsICd0eHRVcmwnKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHVybEZpZWxkICYmIHVybEZpZWxkLmdldFZhbHVlKCkgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKS5zdGFydHNXaXRoKCcvJykpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaWFsb2cuc2VsZWN0UGFnZSgnaW5mbycpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5b+955Wl6ZSZ6K+vCiAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9LCA1MDApCiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKSwgMTAwMDApCiAgICAgICAgICAgICAgICAgICAgICB9LCAxMDAwKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICB0aGlzLmZhbGxiYWNrVG9UZXh0YXJlYSgpCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQoKICAgICAgICAgIC8vIOebkeWQrOWGheWuueWPmOWMlgogICAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLnJpY2hFZGl0b3Iub24pIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLm9uKCdjaGFuZ2UnLCAoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVFZGl0b3JDb250ZW50Q2hhbmdlKCkKICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5vbigna2V5JywgKCkgPT4gewogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVFZGl0b3JDb250ZW50Q2hhbmdlKCkKICAgICAgICAgICAgICB9LCAxMDApCiAgICAgICAgICAgIH0pCgogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iub24oJ2luc3RhbmNlUmVhZHknLCAoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5lZGl0b3JJbml0aWFsaXplZCA9IHRydWUKICAgICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iuc2V0RGF0YSgnJykKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLmZhbGxiYWNrVG9UZXh0YXJlYSgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5aSE55CG57yW6L6R5Zmo5YaF5a655Y+Y5YyWCiAgICBoYW5kbGVFZGl0b3JDb250ZW50Q2hhbmdlKCkgewogICAgICBjb25zdCByYXdDb250ZW50ID0gdGhpcy5yaWNoRWRpdG9yLmdldERhdGEoKQogICAgICBjb25zdCBjb250ZW50V2l0aFJlbGF0aXZlVXJscyA9IHRoaXMuY29udmVydFVybHNUb1JlbGF0aXZlKHJhd0NvbnRlbnQpCiAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9IHRoaXMucHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcoY29udGVudFdpdGhSZWxhdGl2ZVVybHMpCiAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gdGhpcy5zdHJpcEh0bWxUYWdzS2VlcEltYWdlcyhjb250ZW50V2l0aFJlbGF0aXZlVXJscykKICAgIH0sCgogICAgLy8g5Zue6YCA5Yiw5pmu6YCa5paH5pys5qGGCiAgICBmYWxsYmFja1RvVGV4dGFyZWEoKSB7CiAgICAgIGNvbnN0IGVkaXRvckNvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdyaWNoLWVkaXRvcicpCiAgICAgIGlmIChlZGl0b3JDb250YWluZXIpIHsKICAgICAgICBjb25zdCB0ZXh0YXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJykKICAgICAgICB0ZXh0YXJlYS5jbGFzc05hbWUgPSAnZmFsbGJhY2stdGV4dGFyZWEnCiAgICAgICAgdGV4dGFyZWEucGxhY2Vob2xkZXIgPSAn6K+35Zyo5q2k5aSE57KY6LS05oiW6L6T5YWl6aKY55uu5YaF5a65Li4uJwogICAgICAgIHRleHRhcmVhLnZhbHVlID0gJycgLy8g56Gu5L+d5paH5pys5qGG5Li656m6CiAgICAgICAgdGV4dGFyZWEuc3R5bGUuY3NzVGV4dCA9ICd3aWR0aDogMTAwJTsgaGVpZ2h0OiA0MDBweDsgYm9yZGVyOiAxcHggc29saWQgI2RkZDsgcGFkZGluZzogMTBweDsgZm9udC1mYW1pbHk6ICJDb3VyaWVyIE5ldyIsIG1vbm9zcGFjZTsgZm9udC1zaXplOiAxNHB4OyBsaW5lLWhlaWdodDogMS42OyByZXNpemU6IG5vbmU7JwoKICAgICAgICAvLyDnm5HlkKzlhoXlrrnlj5jljJYKICAgICAgICB0ZXh0YXJlYS5hZGRFdmVudExpc3RlbmVyKCdpbnB1dCcsIChlKSA9PiB7CiAgICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IGUudGFyZ2V0LnZhbHVlCiAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSBlLnRhcmdldC52YWx1ZQogICAgICAgIH0pCgogICAgICAgIGVkaXRvckNvbnRhaW5lci5pbm5lckhUTUwgPSAnJwogICAgICAgIGVkaXRvckNvbnRhaW5lci5hcHBlbmRDaGlsZCh0ZXh0YXJlYSkKICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gdHJ1ZQogICAgICB9CiAgICB9LAoKCgogICAgLy8g6K6+572u57yW6L6R5Zmo5YaF5a65CiAgICBzZXRFZGl0b3JDb250ZW50KGNvbnRlbnQpIHsKICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoY29udGVudCkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IGNvbnRlbnQKICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSBjb250ZW50CiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDpmLLmipblh73mlbAKICAgIGRlYm91bmNlKGZ1bmMsIHdhaXQpIHsKICAgICAgbGV0IHRpbWVvdXQKICAgICAgcmV0dXJuIGZ1bmN0aW9uIGV4ZWN1dGVkRnVuY3Rpb24oLi4uYXJncykgewogICAgICAgIGNvbnN0IGxhdGVyID0gKCkgPT4gewogICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpCiAgICAgICAgICBmdW5jKC4uLmFyZ3MpCiAgICAgICAgfQogICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KQogICAgICAgIHRpbWVvdXQgPSBzZXRUaW1lb3V0KGxhdGVyLCB3YWl0KQogICAgICB9CiAgICB9LAoKICAgIC8vIOWwhue8lui+keWZqOWGheWuueS4reeahOWujOaVtFVSTOi9rOaNouS4uuebuOWvuei3r+W+hAogICAgY29udmVydFVybHNUb1JlbGF0aXZlKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50KSByZXR1cm4gY29udGVudAoKICAgICAgLy8g5Yy56YWN5b2T5YmN5Z+f5ZCN55qE5a6M5pW0VVJM5bm26L2s5o2i5Li655u45a+56Lev5b6ECiAgICAgIGNvbnN0IGN1cnJlbnRPcmlnaW4gPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luCiAgICAgIGNvbnN0IHVybFJlZ2V4ID0gbmV3IFJlZ0V4cChjdXJyZW50T3JpZ2luLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXVxcXS9nLCAnXFwkJicpICsgJygvW14iXCdcXHM+XSopJywgJ2cnKQoKICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSh1cmxSZWdleCwgJyQxJykKICAgIH0sCgogICAgLy8g6Kej5p6Q5paH5qGjCiAgICBwYXJzZURvY3VtZW50KCkgewogICAgICBpZiAoIXRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKSkgewogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJzZVJlc3VsdCA9IHRoaXMucGFyc2VRdWVzdGlvbkNvbnRlbnQodGhpcy5kb2N1bWVudENvbnRlbnQpCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBwYXJzZVJlc3VsdC5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICAgIGNvbGxhcHNlZDogZmFsc2UKICAgICAgICB9KSkKICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcGFyc2VSZXN1bHQuZXJyb3JzCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFsn6Kej5p6Q5aSx6LSl77yaJyArIGVycm9yLm1lc3NhZ2VdCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICB9CiAgICB9LAoKICAgIC8vIOino+aekOmimOebruWGheWuuSAtIOS8mOWMlueJiOacrO+8jOabtOWKoOWBpeWjrgogICAgcGFyc2VRdWVzdGlvbkNvbnRlbnQoY29udGVudCkgewogICAgICBjb25zdCBxdWVzdGlvbnMgPSBbXQogICAgICBjb25zdCBlcnJvcnMgPSBbXQoKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewoKICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyfop6PmnpDlhoXlrrnkuLrnqbrmiJbmoLzlvI/kuI3mraPnoa4nXSB9CiAgICAgIH0KCiAgICAgIHRyeSB7CgoKICAgICAgICBjb25zdCB0ZXh0Q29udGVudCA9IHRoaXMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudCkKCiAgICAgICAgaWYgKCF0ZXh0Q29udGVudCB8fCB0ZXh0Q29udGVudC50cmltKCkubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyflpITnkIblkI7nmoTlhoXlrrnkuLrnqbonXSB9CiAgICAgICAgfQoKICAgICAgICBjb25zdCBsaW5lcyA9IHRleHRDb250ZW50LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgogICAgICAgIGlmIChsaW5lcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgIHJldHVybiB7IHF1ZXN0aW9ucywgZXJyb3JzOiBbJ+ayoeacieacieaViOeahOWGheWuueihjCddIH0KICAgICAgICB9CgoKCiAgICAgICAgbGV0IGN1cnJlbnRRdWVzdGlvbkxpbmVzID0gW10KICAgICAgICBsZXQgcXVlc3Rpb25OdW1iZXIgPSAwCgogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+mimOebruW8gOWni+ihjO+8muaVsOWtl+OAgVvpopjnm67nsbvlnotdIOaIliBb6aKY55uu57G75Z6LXQogICAgICAgICAgY29uc3QgaXNRdWVzdGlvblN0YXJ0ID0gdGhpcy5pc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpIHx8IHRoaXMuaXNRdWVzdGlvblR5cGVTdGFydChsaW5lKQoKICAgICAgICAgIGlmIChpc1F1ZXN0aW9uU3RhcnQpIHsKICAgICAgICAgICAgLy8g5aaC5p6c5LmL5YmN5pyJ6aKY55uu5YaF5a6577yM5YWI5aSE55CG5LmL5YmN55qE6aKY55uuCiAgICAgICAgICAgIGlmIChjdXJyZW50UXVlc3Rpb25MaW5lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGNvbnN0IHF1ZXN0aW9uVGV4dCA9IGN1cnJlbnRRdWVzdGlvbkxpbmVzLmpvaW4oJ1xuJykKICAgICAgICAgICAgICAgIGNvbnN0IHBhcnNlZFF1ZXN0aW9uID0gdGhpcy5wYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKHF1ZXN0aW9uVGV4dCwgcXVlc3Rpb25OdW1iZXIpCiAgICAgICAgICAgICAgICBpZiAocGFyc2VkUXVlc3Rpb24pIHsKICAgICAgICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2gocGFyc2VkUXVlc3Rpb24pCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGVycm9ycy5wdXNoKGDnrKwgJHtxdWVzdGlvbk51bWJlcn0g6aKY6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOW8gOWni+aWsOmimOebrgogICAgICAgICAgICBjdXJyZW50UXVlc3Rpb25MaW5lcyA9IFtsaW5lXQogICAgICAgICAgICBxdWVzdGlvbk51bWJlcisrCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDlpoLmnpzlvZPliY3lnKjlpITnkIbpopjnm67kuK3vvIzmt7vliqDliLDlvZPliY3popjnm64KICAgICAgICAgICAgaWYgKGN1cnJlbnRRdWVzdGlvbkxpbmVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBjdXJyZW50UXVlc3Rpb25MaW5lcy5wdXNoKGxpbmUpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOWkhOeQhuacgOWQjuS4gOS4qumimOebrgogICAgICAgIGlmIChjdXJyZW50UXVlc3Rpb25MaW5lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBjb25zdCBxdWVzdGlvblRleHQgPSBjdXJyZW50UXVlc3Rpb25MaW5lcy5qb2luKCdcbicpCiAgICAgICAgICAgIGNvbnN0IHBhcnNlZFF1ZXN0aW9uID0gdGhpcy5wYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKHF1ZXN0aW9uVGV4dCwgcXVlc3Rpb25OdW1iZXIpCiAgICAgICAgICAgIGlmIChwYXJzZWRRdWVzdGlvbikgewogICAgICAgICAgICAgIHF1ZXN0aW9ucy5wdXNoKHBhcnNlZFF1ZXN0aW9uKQogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICBlcnJvcnMucHVzaChg56ysICR7cXVlc3Rpb25OdW1iZXJ9IOmimOino+aekOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApCiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBlcnJvcnMucHVzaChg5paH5qGj6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgfQoKICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnMgfQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpopjnm67lvIDlp4vooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzUXVlc3Rpb25TdGFydExpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrmr4/popjliY3pnaLpnIDopoHliqDkuIrpopjlj7fmoIfor4bvvIzpopjlj7flkI7pnaLpnIDopoHliqDkuIrnrKblj7fvvIg677ya44CBLu+8ju+8iQogICAgICAvLyDljLnphY3moLzlvI/vvJrmlbDlrZcgKyDnrKblj7coOu+8muOAgS7vvI4pICsg5Y+v6YCJ56m65qC8CiAgICAgIC8vIOS+i+Wmgu+8mjEuIDHjgIEgMe+8miAx77yOIOetiQogICAgICByZXR1cm4gL15cZCtbLjrvvJrvvI7jgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumimOWei+agh+azqOW8gOWni+ihjAogICAgaXNRdWVzdGlvblR5cGVTdGFydChsaW5lKSB7CiAgICAgIC8vIOWMuemFjeagvOW8j++8mlvpopjnm67nsbvlnotdCiAgICAgIC8vIOS+i+Wmgu+8mlvljZXpgInpophdIFvlpJrpgInpophdIFvliKTmlq3pophdIOetiQogICAgICByZXR1cm4gL15cWy4qP+mimFxdLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOWNleS4qumimOebriAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VRdWVzdGlvbkZyb21MaW5lcyhxdWVzdGlvblRleHQpIHsKICAgICAgY29uc3QgbGluZXMgPSBxdWVzdGlvblRleHQuc3BsaXQoJ1xuJykubWFwKGxpbmUgPT4gbGluZS50cmltKCkpLmZpbHRlcihsaW5lID0+IGxpbmUubGVuZ3RoID4gMCkKCiAgICAgIGlmIChsaW5lcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+mimOebruWGheWuueS4uuepuicpCiAgICAgIH0KCiAgICAgIGxldCBxdWVzdGlvblR5cGUgPSAnanVkZ21lbnQnIC8vIOm7mOiupOWIpOaWremimAogICAgICBsZXQgcXVlc3Rpb25Db250ZW50ID0gJycKICAgICAgbGV0IGNvbnRlbnRTdGFydEluZGV4ID0gMAoKICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6aKY5Z6L5qCH5rOo77yI5aaCIFvljZXpgInpophd44CBW+WkmumAiemimF3jgIFb5Yik5pat6aKYXe+8iQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCiAgICAgICAgY29uc3QgdHlwZU1hdGNoID0gbGluZS5tYXRjaCgvXFsoLio/6aKYKVxdLykKICAgICAgICBpZiAodHlwZU1hdGNoKSB7CiAgICAgICAgICBjb25zdCB0eXBlVGV4dCA9IHR5cGVNYXRjaFsxXQoKICAgICAgICAgIC8vIOi9rOaNoumimOebruexu+WeiwogICAgICAgICAgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfliKTmlq0nKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnanVkZ21lbnQnCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfljZXpgIknKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnc2luZ2xlJwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5aSa6YCJJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ211bHRpcGxlJwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5aGr56m6JykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2ZpbGwnCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfnroDnrZQnKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnZXNzYXknCiAgICAgICAgICB9CgogICAgICAgICAgLy8g5aaC5p6c6aKY5Z6L5qCH5rOo5ZKM6aKY55uu5YaF5a655Zyo5ZCM5LiA6KGMCiAgICAgICAgICBjb25zdCByZW1haW5pbmdDb250ZW50ID0gbGluZS5yZXBsYWNlKC9cWy4qP+mimFxdLywgJycpLnRyaW0oKQogICAgICAgICAgaWYgKHJlbWFpbmluZ0NvbnRlbnQpIHsKICAgICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gcmVtYWluaW5nQ29udGVudAogICAgICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IGkgKyAxCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IGkgKyAxCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw6aKY5Z6L5qCH5rOo77yM5LuO56ys5LiA6KGM5byA5aeL6Kej5p6QCiAgICAgIGlmIChjb250ZW50U3RhcnRJbmRleCA9PT0gMCkgewogICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gMAogICAgICB9CgogICAgICAvLyDmj5Dlj5bpopjnm67lhoXlrrnvvIjku47popjlj7fooYzlvIDlp4vvvIkKICAgICAgZm9yIChsZXQgaSA9IGNvbnRlbnRTdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g5aaC5p6c5piv6aKY5Y+36KGM77yM5o+Q5Y+W6aKY55uu5YaF5a6577yI56e76Zmk6aKY5Y+377yJCiAgICAgICAgaWYgKHRoaXMuaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSkgewogICAgICAgICAgLy8g56e76Zmk6aKY5Y+377yM5o+Q5Y+W6aKY55uu5YaF5a65CiAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSBsaW5lLnJlcGxhY2UoL15cZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgYnJlYWsKICAgICAgICB9IGVsc2UgaWYgKCFxdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICAgIC8vIOWmguaenOi/mOayoeaciemimOebruWGheWuue+8jOW9k+WJjeihjOWwseaYr+mimOebruWGheWuuQogICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gbGluZQogICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgYnJlYWsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOe7p+e7reaUtumbhumimOebruWGheWuue+8iOebtOWIsOmBh+WIsOmAiemhueaIluetlOahiO+8iQogICAgICBmb3IgKGxldCBpID0gY29udGVudFN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAvLyDlpoLmnpzpgYfliLDpgInpobnooYzjgIHnrZTmoYjooYzjgIHop6PmnpDooYzmiJbpmr7luqbooYzvvIzlgZzmraLmlLbpm4bpopjnm67lhoXlrrkKICAgICAgICBpZiAodGhpcy5pc09wdGlvbkxpbmUobGluZSkgfHwgdGhpcy5pc0Fuc3dlckxpbmUobGluZSkgfHwKICAgICAgICAgICAgdGhpcy5pc0V4cGxhbmF0aW9uTGluZShsaW5lKSB8fCB0aGlzLmlzRGlmZmljdWx0eUxpbmUobGluZSkpIHsKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQoKICAgICAgICAvLyDnu6fnu63mt7vliqDliLDpopjnm67lhoXlrrnvvIzkvYbopoHnoa7kv53kuI3ljIXlkKvpopjlj7cKICAgICAgICBsZXQgY2xlYW5MaW5lID0gbGluZQogICAgICAgIC8vIOWmguaenOi/meihjOi/mOWMheWQq+mimOWPt++8jOenu+mZpOWugwogICAgICAgIGlmICh0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUobGluZSkpIHsKICAgICAgICAgIGNsZWFuTGluZSA9IGxpbmUucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgICAgfQoKICAgICAgICBpZiAoY2xlYW5MaW5lKSB7CiAgICAgICAgICBpZiAocXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCArPSAnXG4nICsgY2xlYW5MaW5lCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSBjbGVhbkxpbmUKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmICghcXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5Xmj5Dlj5bpopjnm67lhoXlrrknKQogICAgICB9CgogICAgICAvLyDmnIDnu4jmuIXnkIbvvJrnoa7kv53popjnm67lhoXlrrnkuI3ljIXlkKvpopjlj7cKICAgICAgbGV0IGZpbmFsUXVlc3Rpb25Db250ZW50ID0gcXVlc3Rpb25Db250ZW50LnRyaW0oKQogICAgICAvLyDkvb/nlKjmm7TlvLrnmoTmuIXnkIbpgLvovpHvvIzlpJrmrKHmuIXnkIbnoa7kv53lvbvlupXnp7vpmaTpopjlj7cKICAgICAgd2hpbGUgKC9eXHMqXGQrWy4677ya77yO44CBXS8udGVzdChmaW5hbFF1ZXN0aW9uQ29udGVudCkpIHsKICAgICAgICBmaW5hbFF1ZXN0aW9uQ29udGVudCA9IGZpbmFsUXVlc3Rpb25Db250ZW50LnJlcGxhY2UoL15ccypcZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICB9CgogICAgICAvLyDpop3lpJbmuIXnkIbvvJrnp7vpmaTlj6/og73nmoRIVE1M5qCH562+5YaF55qE6aKY5Y+3CiAgICAgIGlmIChmaW5hbFF1ZXN0aW9uQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgICAgZmluYWxRdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKGZpbmFsUXVlc3Rpb25Db250ZW50KQogICAgICB9CgogICAgICBjb25zdCBxdWVzdGlvbiA9IHsKICAgICAgICBxdWVzdGlvblR5cGU6IHF1ZXN0aW9uVHlwZSwKICAgICAgICB0eXBlOiBxdWVzdGlvblR5cGUsCiAgICAgICAgdHlwZU5hbWU6IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSksCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBmaW5hbFF1ZXN0aW9uQ29udGVudCwKICAgICAgICBjb250ZW50OiBmaW5hbFF1ZXN0aW9uQ29udGVudCwKICAgICAgICBkaWZmaWN1bHR5OiAnJywgLy8g5LiN6K6+572u6buY6K6k5YC8CiAgICAgICAgZXhwbGFuYXRpb246ICcnLAogICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgIGNvcnJlY3RBbnN3ZXI6ICcnLAogICAgICAgIGNvbGxhcHNlZDogZmFsc2UgIC8vIOm7mOiupOWxleW8gAogICAgICB9CgogICAgICAvLyDop6PmnpDpgInpobnvvIjlr7nkuo7pgInmi6npopjvvIkKICAgICAgY29uc3Qgb3B0aW9uUmVzdWx0ID0gdGhpcy5wYXJzZU9wdGlvbnNGcm9tTGluZXMobGluZXMsIDApCiAgICAgIHF1ZXN0aW9uLm9wdGlvbnMgPSBvcHRpb25SZXN1bHQub3B0aW9ucwoKICAgICAgLy8g5qC55o2u6YCJ6aG55pWw6YeP5o6o5pat6aKY55uu57G75Z6L77yI5aaC5p6c5LmL5YmN5rKh5pyJ5piO56Gu5qCH5rOo77yJCiAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdqdWRnbWVudCcgJiYgcXVlc3Rpb24ub3B0aW9ucy5sZW5ndGggPiAwKSB7CiAgICAgICAgLy8g5aaC5p6c5pyJ6YCJ6aG577yM5o6o5pat5Li66YCJ5oup6aKYCiAgICAgICAgcXVlc3Rpb25UeXBlID0gJ3NpbmdsZScgIC8vIOm7mOiupOS4uuWNlemAiemimAogICAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgIHF1ZXN0aW9uLnR5cGUgPSBxdWVzdGlvblR5cGUKICAgICAgICBxdWVzdGlvbi50eXBlTmFtZSA9IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSkKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q562U5qGI44CB6Kej5p6Q44CB6Zq+5bqmCiAgICAgIHRoaXMucGFyc2VRdWVzdGlvbk1ldGFGcm9tTGluZXMobGluZXMsIHF1ZXN0aW9uKQoKICAgICAgLy8g5qC55o2u562U5qGI6ZW/5bqm6L+b5LiA5q2l5o6o5pat6YCJ5oup6aKY57G75Z6LCiAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdzaW5nbGUnICYmIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgJiYgcXVlc3Rpb24uY29ycmVjdEFuc3dlci5sZW5ndGggPiAxKSB7CiAgICAgICAgLy8g5aaC5p6c562U5qGI5YyF5ZCr5aSa5Liq5a2X5q+N77yM5o6o5pat5Li65aSa6YCJ6aKYCiAgICAgICAgaWYgKC9eW0EtWl17Mix9JC8udGVzdChxdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSkgewogICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ211bHRpcGxlJwogICAgICAgICAgcXVlc3Rpb24ucXVlc3Rpb25UeXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgICBxdWVzdGlvbi50eXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgICBxdWVzdGlvbi50eXBlTmFtZSA9IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOacgOe7iOa4heeQhu+8muehruS/nemimOebruWGheWuueWujOWFqOayoeaciemimOWPtwogICAgICBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCkKICAgICAgcXVlc3Rpb24uY29udGVudCA9IHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudAoKICAgICAgcmV0dXJuIHF1ZXN0aW9uCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumAiemhueihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNPcHRpb25MaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya6YCJ6aG55qC85byP77yIQTrvvInvvIzlrZfmr43lj6/ku6XkuLpB5YiwWueahOS7u+aEj+Wkp+Wwj+WGmeWtl+avje+8jOWGkuWPt+WPr+S7peabv+aNouS4uiI677ya44CBLu+8jiLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9eW0EtWmEtel1bLjrvvJrvvI7jgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uuetlOahiOihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNBbnN3ZXJMaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya5pi+5byP5qCH5rOo5qC85byP77yI562U5qGI77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICByZXR1cm4gL17nrZTmoYhbLjrvvJrjgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uuino+aekOihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNFeHBsYW5hdGlvbkxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrop6PmnpDmoLzlvI/vvIjop6PmnpDvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgIHJldHVybiAvXuino+aekFsuOu+8muOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66Zq+5bqm6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc0RpZmZpY3VsdHlMaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya6Zq+5bqm5qC85byP77yI6Zq+5bqm77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICByZXR1cm4gL17pmr7luqZbLjrvvJrjgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOiOt+WPlumimOebruexu+Wei+aYvuekuuWQjeensAogICAgZ2V0VHlwZURpc3BsYXlOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnanVkZ21lbnQnOiAn5Yik5pat6aKYJywKICAgICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICAgJ211bHRpcGxlJzogJ+WkmumAiemimCcsCiAgICAgICAgJ2ZpbGwnOiAn5aGr56m66aKYJywKICAgICAgICAnZXNzYXknOiAn566A562U6aKYJwogICAgICB9CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfliKTmlq3popgnCiAgICB9LAoKICAgIC8vIOWkhOeQhuWbvueJh+i3r+W+hO+8jOWwhuebuOWvuei3r+W+hOi9rOaNouS4uuWujOaVtOi3r+W+hAogICAgcHJvY2Vzc0ltYWdlUGF0aHMoY29udGVudCkgewogICAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcHJvY2Vzc2VkQ29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGltZyhbXj5dKj8pc3JjPSIoW14iXSo/KSIoW14+XSo/KT4vZywgKG1hdGNoLCBiZWZvcmUsIHNyYywgYWZ0ZXIpID0+IHsKICAgICAgICAgIGlmICghc3JjKSByZXR1cm4gbWF0Y2gKCiAgICAgICAgICBpZiAoc3JjLnN0YXJ0c1dpdGgoJ2h0dHA6Ly8nKSB8fCBzcmMuc3RhcnRzV2l0aCgnaHR0cHM6Ly8nKSB8fCBzcmMuc3RhcnRzV2l0aCgnZGF0YTonKSkgewogICAgICAgICAgICByZXR1cm4gbWF0Y2gKICAgICAgICAgIH0KCiAgICAgICAgICBjb25zdCBmdWxsU3JjID0gJ2h0dHA6Ly9sb2NhbGhvc3Q6ODgwMicgKyAoc3JjLnN0YXJ0c1dpdGgoJy8nKSA/IHNyYyA6ICcvJyArIHNyYykKICAgICAgICAgIHJldHVybiBgPGltZyR7YmVmb3JlfXNyYz0iJHtmdWxsU3JjfSIke2FmdGVyfT5gCiAgICAgICAgfSkKCiAgICAgICAgcmV0dXJuIHByb2Nlc3NlZENvbnRlbnQKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CiAgICB9LAoKICAgIC8vIOS/neeVmeWvjOaWh+acrOagvOW8j+eUqOS6jumihOiniOaYvuekugogICAgcHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcoY29udGVudCkgewogICAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5L+d55WZ5bi455So55qE5a+M5paH5pys5qC85byP5qCH562+CiAgICAgICAgbGV0IHByb2Nlc3NlZENvbnRlbnQgPSBjb250ZW50CiAgICAgICAgICAvLyDovazmjaLnm7jlr7not6/lvoTnmoTlm77niYcKICAgICAgICAgIC5yZXBsYWNlKC88aW1nKFtePl0qPylzcmM9IihbXiJdKj8pIihbXj5dKj8pPi9naSwgKG1hdGNoLCBiZWZvcmUsIHNyYywgYWZ0ZXIpID0+IHsKICAgICAgICAgICAgaWYgKCFzcmMuc3RhcnRzV2l0aCgnaHR0cCcpICYmICFzcmMuc3RhcnRzV2l0aCgnZGF0YTonKSkgewogICAgICAgICAgICAgIGNvbnN0IGZ1bGxTcmMgPSB0aGlzLnByb2Nlc3NJbWFnZVBhdGhzKHNyYykKICAgICAgICAgICAgICByZXR1cm4gYDxpbWcke2JlZm9yZX1zcmM9IiR7ZnVsbFNyY30iJHthZnRlcn0+YAogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBtYXRjaAogICAgICAgICAgfSkKICAgICAgICAgIC8vIOS/neeVmeauteiQvee7k+aehAogICAgICAgICAgLnJlcGxhY2UoLzxwW14+XSo+L2dpLCAnPHA+JykKICAgICAgICAgIC5yZXBsYWNlKC88XC9wPi9naSwgJzwvcD4nKQogICAgICAgICAgLy8g5L+d55WZ5o2i6KGMCiAgICAgICAgICAucmVwbGFjZSgvPGJyXHMqXC8/Pi9naSwgJzxicj4nKQogICAgICAgICAgLy8g5riF55CG5aSa5L2Z55qE56m655m95q616JC9CiAgICAgICAgICAucmVwbGFjZSgvPHA+XHMqPFwvcD4vZ2ksICcnKQogICAgICAgICAgLnJlcGxhY2UoLyg8cD5bXHNcbl0qPFwvcD4pL2dpLCAnJykKCiAgICAgICAgcmV0dXJuIHByb2Nlc3NlZENvbnRlbnQudHJpbSgpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgcmV0dXJuIGNvbnRlbnQKICAgICAgfQogICAgfSwKCiAgICAvLyDnp7vpmaRIVE1M5qCH562+5L2G5L+d55WZ5Zu+54mH5qCH562+CiAgICBzdHJpcEh0bWxUYWdzS2VlcEltYWdlcyhjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBpbWFnZXMgPSBbXQogICAgICAgIGxldCBpbWFnZUluZGV4ID0gMAogICAgICAgIGNvbnN0IGNvbnRlbnRXaXRoUGxhY2Vob2xkZXJzID0gY29udGVudC5yZXBsYWNlKC88aW1nW14+XSo+L2dpLCAobWF0Y2gpID0+IHsKICAgICAgICAgIGltYWdlcy5wdXNoKG1hdGNoKQogICAgICAgICAgcmV0dXJuIGBcbl9fSU1BR0VfUExBQ0VIT0xERVJfJHtpbWFnZUluZGV4Kyt9X19cbmAKICAgICAgICB9KQoKICAgICAgICBsZXQgdGV4dENvbnRlbnQgPSBjb250ZW50V2l0aFBsYWNlaG9sZGVycwogICAgICAgICAgLnJlcGxhY2UoLzxiclxzKlwvPz4vZ2ksICdcbicpCiAgICAgICAgICAucmVwbGFjZSgvPFwvcD4vZ2ksICdcbicpCiAgICAgICAgICAucmVwbGFjZSgvPHBbXj5dKj4vZ2ksICdcbicpCiAgICAgICAgICAucmVwbGFjZSgvPFtePl0qPi9nLCAnJykKICAgICAgICAgIC5yZXBsYWNlKC9cblxzKlxuL2csICdcbicpCgogICAgICAgIGxldCBmaW5hbENvbnRlbnQgPSB0ZXh0Q29udGVudAogICAgICAgIGltYWdlcy5mb3JFYWNoKChpbWcsIGluZGV4KSA9PiB7CiAgICAgICAgICBjb25zdCBwbGFjZWhvbGRlciA9IGBfX0lNQUdFX1BMQUNFSE9MREVSXyR7aW5kZXh9X19gCiAgICAgICAgICBpZiAoZmluYWxDb250ZW50LmluY2x1ZGVzKHBsYWNlaG9sZGVyKSkgewogICAgICAgICAgICBmaW5hbENvbnRlbnQgPSBmaW5hbENvbnRlbnQucmVwbGFjZShwbGFjZWhvbGRlciwgaW1nKQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIHJldHVybiBmaW5hbENvbnRlbnQudHJpbSgpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgcmV0dXJuIGNvbnRlbnQKICAgICAgfQogICAgfSwKCiAgICAvLyDku47ooYzmlbDnu4Top6PmnpDpgInpobkgLSDmjInnhafovpPlhaXop4TojIMKICAgIHBhcnNlT3B0aW9uc0Zyb21MaW5lcyhsaW5lcywgc3RhcnRJbmRleCkgewogICAgICBjb25zdCBvcHRpb25zID0gW10KCiAgICAgIGlmICghQXJyYXkuaXNBcnJheShsaW5lcykgfHwgc3RhcnRJbmRleCA8IDAgfHwgc3RhcnRJbmRleCA+PSBsaW5lcy5sZW5ndGgpIHsKICAgICAgICByZXR1cm4geyBvcHRpb25zIH0KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKGxldCBpID0gc3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgICBpZiAoIWxpbmUgfHwgdHlwZW9mIGxpbmUgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgICB9CgogICAgICAgICAgLy8g6KeE6IyD77ya6YCJ6aG55qC85byP77yIQTrvvInvvIzlrZfmr43lj6/ku6XkuLpB5YiwWueahOS7u+aEj+Wkp+Wwj+WGmeWtl+avje+8jOWGkuWPt+WPr+S7peabv+aNouS4uiI677ya44CBLu+8jiLlhbbkuK3kuYvkuIAKICAgICAgICAgIGNvbnN0IG9wdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgICAgICBpZiAob3B0aW9uTWF0Y2gpIHsKICAgICAgICAgICAgY29uc3Qgb3B0aW9uS2V5ID0gb3B0aW9uTWF0Y2hbMV0udG9VcHBlckNhc2UoKQogICAgICAgICAgICBjb25zdCBvcHRpb25Db250ZW50ID0gb3B0aW9uTWF0Y2hbMl0gPyBvcHRpb25NYXRjaFsyXS50cmltKCkgOiAnJwoKICAgICAgICAgICAgaWYgKG9wdGlvbktleSAmJiBvcHRpb25Db250ZW50KSB7CiAgICAgICAgICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IG9wdGlvbkNvbnRlbnQsCiAgICAgICAgICAgICAgICBjb250ZW50OiBvcHRpb25Db250ZW50CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmlzQW5zd2VyTGluZShsaW5lKSB8fCB0aGlzLmlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNEaWZmaWN1bHR5TGluZShsaW5lKSkgewogICAgICAgICAgICAvLyDpgYfliLDnrZTmoYjjgIHop6PmnpDmiJbpmr7luqbooYzvvIzlgZzmraLop6PmnpDpgInpobkKICAgICAgICAgICAgYnJlYWsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOinhOiMg++8mumAiemhueS4jumAiemhueS5i+mXtO+8jOWPr+S7peaNouihjO+8jOS5n+WPr+S7peWcqOWQjOS4gOihjAogICAgICAgICAgICAvLyDlpoLmnpzpgInpobnlnKjlkIzkuIDooYzvvIzpgInpobnkuYvpl7Toh7PlsJHpnIDopoHmnInkuIDkuKrnqbrmoLwKICAgICAgICAgICAgY29uc3QgbXVsdGlwbGVPcHRpb25zTWF0Y2ggPSBsaW5lLm1hdGNoKC8oW0EtWmEtel1bLjrvvJrvvI7jgIFdXHMqW15cc10rKD86XHMrW0EtWmEtel1bLjrvvJrvvI7jgIFdXHMqW15cc10rKSopL2cpCiAgICAgICAgICAgIGlmIChtdWx0aXBsZU9wdGlvbnNNYXRjaCkgewogICAgICAgICAgICAgIC8vIOWkhOeQhuWQjOS4gOihjOWkmuS4qumAiemhueeahOaDheWGtQogICAgICAgICAgICAgIGNvbnN0IHNpbmdsZU9wdGlvbnMgPSBsaW5lLnNwbGl0KC9ccysoPz1bQS1aYS16XVsuOu+8mu+8juOAgV0pLykKICAgICAgICAgICAgICBmb3IgKGNvbnN0IHNpbmdsZU9wdGlvbiBvZiBzaW5nbGVPcHRpb25zKSB7CiAgICAgICAgICAgICAgICBpZiAoIXNpbmdsZU9wdGlvbikgY29udGludWUKCiAgICAgICAgICAgICAgICBjb25zdCBtYXRjaCA9IHNpbmdsZU9wdGlvbi5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgICAgICAgICAgICBpZiAobWF0Y2gpIHsKICAgICAgICAgICAgICAgICAgY29uc3Qgb3B0aW9uS2V5ID0gbWF0Y2hbMV0udG9VcHBlckNhc2UoKQogICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb25Db250ZW50ID0gbWF0Y2hbMl0gPyBtYXRjaFsyXS50cmltKCkgOiAnJwoKICAgICAgICAgICAgICAgICAgaWYgKG9wdGlvbktleSAmJiBvcHRpb25Db250ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IG9wdGlvbkNvbnRlbnQsCiAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBvcHRpb25Db250ZW50CiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAvLyDlv73nlaXplJnor68KICAgICAgfQoKICAgICAgcmV0dXJuIHsgb3B0aW9ucyB9CiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOmimOebruWFg+S/oeaBryAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VRdWVzdGlvbk1ldGFGcm9tTGluZXMobGluZXMsIHF1ZXN0aW9uKSB7CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g6KeE6IyD77ya5pi+5byP5qCH5rOo5qC85byP77yI562U5qGI77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICAgIGNvbnN0IGFuc3dlck1hdGNoID0gbGluZS5tYXRjaCgvXuetlOahiFsuOu+8muOAgV1ccyooLispLykKICAgICAgICBpZiAoYW5zd2VyTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLnBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyTWF0Y2hbMV0sIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrop6PmnpDmoLzlvI/vvIjop6PmnpDvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgICAgY29uc3QgZXhwbGFuYXRpb25NYXRjaCA9IGxpbmUubWF0Y2goL17op6PmnpBbLjrvvJrjgIFdXHMqKC4rKS8pCiAgICAgICAgaWYgKGV4cGxhbmF0aW9uTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmV4cGxhbmF0aW9uID0gZXhwbGFuYXRpb25NYXRjaFsxXS50cmltKCkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrpmr7luqbmoLzlvI/vvIjpmr7luqbvvJrvvInvvIzlj6rmlK/mjIHnroDljZXjgIHkuK3nrYnjgIHlm7Dpmr7kuInkuKrnuqfliKsKICAgICAgICBjb25zdCBkaWZmaWN1bHR5TWF0Y2ggPSBsaW5lLm1hdGNoKC9e6Zq+5bqmWy4677ya44CBXVxzKijnroDljZV85Lit562JfOWbsOmavnzkuK0pLykKICAgICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgICBsZXQgZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXRjaFsxXQogICAgICAgICAgLy8g5qCH5YeG5YyW6Zq+5bqm5YC877ya5bCGIuS4rSLnu5/kuIDkuLoi5Lit562JIgogICAgICAgICAgaWYgKGRpZmZpY3VsdHkgPT09ICfkuK0nKSB7CiAgICAgICAgICAgIGRpZmZpY3VsdHkgPSAn5Lit562JJwogICAgICAgICAgfQogICAgICAgICAgLy8g5Y+q5o6l5Y+X5qCH5YeG55qE5LiJ5Liq6Zq+5bqm57qn5YirCiAgICAgICAgICBpZiAoWyfnroDljZUnLCAn5Lit562JJywgJ+WbsOmaviddLmluY2x1ZGVzKGRpZmZpY3VsdHkpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uLmRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5CiAgICAgICAgICB9CiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6KeE6IyD77ya562U5qGI5pSv5oyB55u05o6l5Zyo6aKY5bmy5Lit5qCH5rOo77yM5LyY5YWI5Lul5pi+5byP5qCH5rOo55qE562U5qGI5Li65YeGCiAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOaYvuW8j+etlOahiO+8jOWwneivleS7jumimOebruWGheWuueS4reaPkOWPlgogICAgICBpZiAoIXF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIpIHsKICAgICAgICBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyID0gdGhpcy5leHRyYWN0QW5zd2VyRnJvbVF1ZXN0aW9uQ29udGVudChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgfQogICAgfSwKCiAgICAvLyDku47popjlubLkuK3mj5Dlj5bnrZTmoYggLSDmjInnhafovpPlhaXop4TojIMKICAgIGV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50KHF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmICghcXVlc3Rpb25Db250ZW50IHx8IHR5cGVvZiBxdWVzdGlvbkNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g6KeE6IyD77ya6aKY5bmy5Lit5qC85byP77yI44CQQeOAke+8ie+8jOaLrOWPt+WPr+S7peabv+aNouS4uuS4reiLseaWh+eahOWwj+aLrOWPt+aIluiAheS4reaLrOWPtwogICAgICAgIGNvbnN0IHBhdHRlcm5zID0gWwogICAgICAgICAgL+OAkChbXuOAkV0rKeOAkS9nLCAgICAvLyDkuK3mlofmlrnmi6zlj7cKICAgICAgICAgIC9cWyhbXlxdXSspXF0vZywgICAvLyDoi7Hmlofmlrnmi6zlj7cKICAgICAgICAgIC/vvIgoW17vvIldKynvvIkvZywgICAgLy8g5Lit5paH5ZyG5ous5Y+3CiAgICAgICAgICAvXCgoW14pXSspXCkvZyAgICAgLy8g6Iux5paH5ZyG5ous5Y+3CiAgICAgICAgXQoKICAgICAgICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgcGF0dGVybnMpIHsKICAgICAgICAgIGNvbnN0IG1hdGNoZXMgPSBxdWVzdGlvbkNvbnRlbnQubWF0Y2gocGF0dGVybikKICAgICAgICAgIGlmIChtYXRjaGVzICYmIG1hdGNoZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAvLyDmj5Dlj5bmnIDlkI7kuIDkuKrljLnphY3pobnkvZzkuLrnrZTmoYjvvIjpgJrluLjnrZTmoYjlnKjpopjnm67mnKvlsL7vvIkKICAgICAgICAgICAgY29uc3QgbGFzdE1hdGNoID0gbWF0Y2hlc1ttYXRjaGVzLmxlbmd0aCAtIDFdCiAgICAgICAgICAgIGNvbnN0IGFuc3dlciA9IGxhc3RNYXRjaC5yZXBsYWNlKC9b44CQ44CRXFtcXe+8iO+8iSgpXS9nLCAnJykudHJpbSgpCgogICAgICAgICAgICBpZiAoYW5zd2VyKSB7CiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXIsIHF1ZXN0aW9uVHlwZSkKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgIH0KCiAgICAgIHJldHVybiAnJwogICAgfSwKCiAgICAvLyDop6PmnpDnrZTmoYjlgLwKICAgIHBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyVGV4dCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmICghYW5zd2VyVGV4dCB8fCB0eXBlb2YgYW5zd2VyVGV4dCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCB0cmltbWVkQW5zd2VyID0gYW5zd2VyVGV4dC50cmltKCkKCiAgICAgICAgaWYgKCF0cmltbWVkQW5zd2VyKSB7CiAgICAgICAgICByZXR1cm4gJycKICAgICAgICB9CgogICAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdqdWRnbWVudCcpIHsKICAgICAgICAgIC8vIOWIpOaWremimOetlOahiOWkhOeQhiAtIOS/neaMgeWOn+Wni+agvOW8j++8jOS4jei9rOaNouS4unRydWUvZmFsc2UKICAgICAgICAgIHJldHVybiB0cmltbWVkQW5zd2VyCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOmAieaLqemimOetlOahiOWkhOeQhgogICAgICAgICAgcmV0dXJuIHRyaW1tZWRBbnN3ZXIudG9VcHBlckNhc2UoKQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHJldHVybiBhbnN3ZXJUZXh0IHx8ICcnCiAgICAgICAgfQogICAgfSwKCgoKCgogICAgLy8g6I635Y+W5qC85byP5YyW55qE6aKY55uu5YaF5a6577yI5pSv5oyB5a+M5paH5pys5qC85byP77yJCiAgICBnZXRGb3JtYXR0ZWRRdWVzdGlvbkNvbnRlbnQocXVlc3Rpb24pIHsKICAgICAgaWYgKCFxdWVzdGlvbiB8fCAhcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIGxldCBjb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50CgogICAgICAvLyDlpoLmnpzmnIlIVE1M5YaF5a655LiU5YyF5ZCr5a+M5paH5pys5qCH562+77yM5LyY5YWI5L2/55SoSFRNTOWGheWuuQogICAgICBpZiAodGhpcy5kb2N1bWVudEh0bWxDb250ZW50ICYmIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgICAgLy8g5LuOSFRNTOWGheWuueS4reaPkOWPluWvueW6lOeahOmimOebruWGheWuuQogICAgICAgIGNvbnN0IGh0bWxDb250ZW50ID0gdGhpcy5leHRyYWN0UXVlc3Rpb25Gcm9tSHRtbChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCkKICAgICAgICBpZiAoaHRtbENvbnRlbnQpIHsKICAgICAgICAgIGNvbnRlbnQgPSBodG1sQ29udGVudAogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5riF55CG6aKY5Y+377ya56Gu5L+d6aKY55uu5YaF5a655LiN5Lul5pWw5a2XK+espuWPt+W8gOWktAogICAgICBjb250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihjb250ZW50KQoKICAgICAgcmV0dXJuIHRoaXMucHJvY2Vzc0ltYWdlUGF0aHMoY29udGVudCkKICAgIH0sCgogICAgLy8g6I635Y+W6aKY5Z6L5ZCN56ewCiAgICBnZXRRdWVzdGlvblR5cGVOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICAgJ211bHRpcGxlJzogJ+WkmumAiemimCcsCiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5pyq55+lJwogICAgfSwKCiAgICAvLyDmuIXnkIbpopjnm67lhoXlrrnkuK3nmoTpopjlj7cKICAgIHJlbW92ZVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhkhUTUzlhoXlrrkKICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJzwnKSkgewogICAgICAgIC8vIOWvueS6jkhUTUzlhoXlrrnvvIzpnIDopoHmuIXnkIbmoIfnrb7lhoXnmoTpopjlj7cKICAgICAgICByZXR1cm4gY29udGVudC5yZXBsYWNlKC88cFtePl0qPihccypcZCtbLjrvvJrvvI7jgIFdXHMqKSguKj8pPFwvcD4vZ2ksICc8cD4kMjwvcD4nKQogICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXihccypcZCtbLjrvvJrvvI7jgIFdXHMqKS8sICcnKSAvLyDmuIXnkIblvIDlpLTnmoTpopjlj7cKICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoLz5ccypcZCtbLjrvvJrvvI7jgIFdXHMqL2csICc+JykgLy8g5riF55CG5qCH562+5ZCO55qE6aKY5Y+3CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5a+55LqO57qv5paH5pys5YaF5a6577yM55u05o6l5riF55CG5byA5aS055qE6aKY5Y+3CiAgICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvXlxzKlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5LuOSFRNTOWGheWuueS4reaPkOWPluWvueW6lOeahOmimOebruWGheWuuQogICAgZXh0cmFjdFF1ZXN0aW9uRnJvbUh0bWwocGxhaW5Db250ZW50LCBodG1sQ29udGVudCkgewogICAgICBpZiAoIXBsYWluQ29udGVudCB8fCAhaHRtbENvbnRlbnQpIHsKICAgICAgICByZXR1cm4gcGxhaW5Db250ZW50CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g566A5Y2V55qE5Yy56YWN562W55Wl77ya5p+l5om+5YyF5ZCr6aKY55uu5YaF5a6555qESFRNTOauteiQvQogICAgICAgIGNvbnN0IHBsYWluVGV4dCA9IHBsYWluQ29udGVudC5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKCiAgICAgICAgLy8g5ZyoSFRNTOWGheWuueS4reafpeaJvuWMheWQq+i/meS4quaWh+acrOeahOauteiQvQogICAgICAgIGNvbnN0IHBhcmFncmFwaHMgPSBodG1sQ29udGVudC5tYXRjaCgvPHBbXj5dKj4uKj88XC9wPi9naSkgfHwgW10KCiAgICAgICAgZm9yIChjb25zdCBwYXJhZ3JhcGggb2YgcGFyYWdyYXBocykgewogICAgICAgICAgY29uc3QgcGFyYWdyYXBoVGV4dCA9IHBhcmFncmFwaC5yZXBsYWNlKC88W14+XSo+L2csICcnKS50cmltKCkKICAgICAgICAgIC8vIOa4heeQhuauteiQveaWh+acrOS4reeahOmimOWPt+WGjei/m+ihjOWMuemFjQogICAgICAgICAgY29uc3QgY2xlYW5QYXJhZ3JhcGhUZXh0ID0gcGFyYWdyYXBoVGV4dC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgICAgIGlmIChjbGVhblBhcmFncmFwaFRleHQuaW5jbHVkZXMocGxhaW5UZXh0LnN1YnN0cmluZygwLCAyMCkpKSB7CiAgICAgICAgICAgIC8vIOaJvuWIsOWMuemFjeeahOauteiQve+8jOi/lOWbnkhUTUzmoLzlvI/vvIjkvYbopoHmuIXnkIbpopjlj7fvvIkKICAgICAgICAgICAgcmV0dXJuIHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIocGFyYWdyYXBoKQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgcmV0dXJuIHBsYWluQ29udGVudAogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHJldHVybiBwbGFpbkNvbnRlbnQKICAgICAgfQogICAgfSwKCgogICAgLy8g5pCc57SiCiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgfSwKICAgIC8vIOmHjee9ruaQnOe0ogogICAgcmVzZXRTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25UeXBlID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpZmZpY3VsdHkgPSBudWxsCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25Db250ZW50ID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqgBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel',\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.handleEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              setTimeout(() => {\n                this.handleEditorContentChange()\n              }, 100)\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化\n    handleEditorContentChange() {\n      const rawContent = this.richEditor.getData()\n      const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n      this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n      this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"]}]}