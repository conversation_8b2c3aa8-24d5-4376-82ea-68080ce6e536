package com.ruoyi.biz.controller;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.QuestionBank;
import com.ruoyi.biz.domain.Question;
import com.ruoyi.biz.service.IQuestionBankService;
import com.ruoyi.biz.service.IQuestionService;
import com.ruoyi.biz.dto.QuestionDTO;
import com.ruoyi.biz.converter.QuestionConverter;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.ss.usermodel.*;

/**
 * 题库Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/biz/questionBank")
public class QuestionBankController extends BaseController
{
    @Autowired
    private IQuestionBankService questionBankService;

    @Autowired
    private IQuestionService questionService;

    @Autowired
    private QuestionConverter questionConverter;

    /**
     * 查询题库列表
     */
    @PreAuthorize("@ss.hasPermi('biz:questionBank:list')")
    @GetMapping("/list")
    public TableDataInfo list(QuestionBank questionBank)
    {
        startPage();
        List<QuestionBank> list = questionBankService.selectQuestionBankList(questionBank);
        return getDataTable(list);
    }

    /**
     * 导出题库列表
     */
    @PreAuthorize("@ss.hasPermi('biz:questionBank:export')")
    @Log(title = "题库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuestionBank questionBank)
    {
        List<QuestionBank> list = questionBankService.selectQuestionBankList(questionBank);
        ExcelUtil<QuestionBank> util = new ExcelUtil<QuestionBank>(QuestionBank.class);
        util.exportExcel(response, list, "题库数据");
    }

    /**
     * 获取题库详细信息
     */
    @PreAuthorize("@ss.hasPermi('biz:questionBank:query')")
    @GetMapping(value = "/{bankId}")
    public AjaxResult getInfo(@PathVariable("bankId") Long bankId)
    {
        return success(questionBankService.selectQuestionBankByBankId(bankId));
    }

    /**
     * 新增题库
     */
    @PreAuthorize("@ss.hasPermi('biz:questionBank:add')")
    @Log(title = "题库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuestionBank questionBank)
    {
        return toAjax(questionBankService.insertQuestionBank(questionBank));
    }

    /**
     * 修改题库
     */
    @PreAuthorize("@ss.hasPermi('biz:questionBank:edit')")
    @Log(title = "题库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionBank questionBank)
    {
        return toAjax(questionBankService.updateQuestionBank(questionBank));
    }

    /**
     * 删除题库
     */
    @PreAuthorize("@ss.hasPermi('biz:questionBank:remove')")
    @Log(title = "题库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bankIds}")
    public AjaxResult remove(@PathVariable Long[] bankIds)
    {
        return toAjax(questionBankService.deleteQuestionBankByBankIds(bankIds));
    }

    /**
     * 下载Excel导入模板
     */
    @PostMapping("/downloadExcelTemplate")
    public void downloadExcelTemplate(HttpServletResponse response)
    {
        try
        {
            ClassPathResource resource = new ClassPathResource("static/题目导入Excel模板.xlsx");
            InputStream inputStream = resource.getInputStream();

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode("题目导入Excel模板.xlsx", "UTF-8"));

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            inputStream.close();
            response.getOutputStream().flush();
        }
        catch (IOException e)
        {
            log.error("下载Excel模板失败", e);
        }
    }

    /**
     * 下载Word导入模板
     */
    @PostMapping("/downloadWordTemplate")
    public void downloadWordTemplate(HttpServletResponse response)
    {
        try
        {
            ClassPathResource resource = new ClassPathResource("static/题目导入Word模板.docx");
            InputStream inputStream = resource.getInputStream();

            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode("题目导入Word模板.docx", "UTF-8"));

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            inputStream.close();
            response.getOutputStream().flush();
        }
        catch (IOException e)
        {
            log.error("下载Word模板失败", e);
        }
    }

    /**
     * 上传文档并解析
     */
    @PostMapping("/uploadDocument")
    public AjaxResult uploadDocument(@RequestParam("file") MultipartFile file, @RequestParam("bankId") Long bankId)
    {
        try
        {
            log.info("接收到文档上传请求，文件名: {}, 大小: {}, 题库ID: {}", file.getOriginalFilename(), file.getSize(), bankId);

            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".docx") && !fileName.endsWith(".xlsx"))) {
                return AjaxResult.error("只支持上传.docx或.xlsx格式的文件");
            }

            // 验证文件大小（10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return AjaxResult.error("文件大小不能超过10MB");
            }

            // 读取文件内容
            String content = "";
            if (fileName.endsWith(".docx")) {
                content = readWordDocument(file);
            } else if (fileName.endsWith(".xlsx")) {
                content = readExcelDocument(file);
            }

            log.info("文档内容读取完成，内容长度: {}", content.length());
            log.debug("文档内容: {}", content.substring(0, Math.min(content.length(), 500)) + "...");

            // 解析题目
            // 解析题目并收集错误信息
            List<String> errors = new ArrayList<>();
            List<Map<String, Object>> questions = parseQuestions(content, errors);

            // 生成格式化的HTML内容
            String documentTitle = extractDocumentTitle(content);
            String formattedContent = generateFormattedHtml(questions, documentTitle);

            log.info("解析完成，题目数量: {}, 错误数量: {}", questions.size(), errors.size());

            AjaxResult result = AjaxResult.success("文档上传成功");
            result.put("originalContent", formattedContent);
            result.put("questions", questions);
            result.put("errors", errors);

            return result;
        }
        catch (Exception e)
        {
            log.error("文档上传失败", e);
            return AjaxResult.error("文档上传失败: " + e.getMessage());
        }
    }

    /**
     * 读取Word文档内容
     */
    private String readWordDocument(MultipartFile file) throws Exception {
        StringBuilder content = new StringBuilder();

        try (java.io.InputStream inputStream = file.getInputStream();
             XWPFDocument document = new XWPFDocument(inputStream)) {

            // 读取所有段落
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph paragraph : paragraphs) {
                String text = paragraph.getText().trim();
                if (!text.isEmpty()) {
                    content.append(text).append("\n");
                }
            }

            log.info("Word文档解析完成，段落数: {}", paragraphs.size());
            return content.toString();
        }
    }

    /**
     * 读取Excel文档内容
     */
    private String readExcelDocument(MultipartFile file) throws Exception {
        StringBuilder content = new StringBuilder();

        try (java.io.InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            for (Row row : sheet) {
                StringBuilder rowContent = new StringBuilder();
                for (Cell cell : row) {
                    String cellValue = "";
                    switch (cell.getCellType()) {
                        case STRING:
                            cellValue = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            cellValue = String.valueOf((int) cell.getNumericCellValue());
                            break;
                        case BOOLEAN:
                            cellValue = String.valueOf(cell.getBooleanCellValue());
                            break;
                        default:
                            cellValue = "";
                    }
                    if (!cellValue.trim().isEmpty()) {
                        if (rowContent.length() > 0) {
                            rowContent.append(" ");
                        }
                        rowContent.append(cellValue.trim());
                    }
                }
                if (rowContent.length() > 0) {
                    content.append(rowContent.toString()).append("\n");
                }
            }

            log.info("Excel文档解析完成，行数: {}", sheet.getLastRowNum() + 1);
            return content.toString();
        }
    }



    /**
     * 解析题目内容
     */
    private List<Map<String, Object>> parseQuestions(String content, List<String> errors) {
        List<Map<String, Object>> questions = new ArrayList<>();

        // 按行分割内容
        String[] lines = content.split("\\n");

        // 重置解析状态，确保每次解析都是干净的开始
        Map<String, Object> currentQuestion = null;
        String currentType = "";
        int processedQuestions = 0;
        int skippedLines = 0;

        log.info("开始解析题目，总行数: {}", lines.length);

        // 打印前50行内容用于调试，并显示每行的字符详情
        log.info("=== 文档内容前50行调试信息 ===");
        for (int i = 0; i < Math.min(50, lines.length); i++) {
            String line = lines[i];
            log.info("第{}行: [{}] (长度:{}, 首字符:{}, ASCII:{})",
                i + 1,
                line,
                line.length(),
                line.length() > 0 ? line.charAt(0) : "空",
                line.length() > 0 ? (int)line.charAt(0) : 0
            );
        }
        log.info("=== 调试信息结束 ===");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.isEmpty()) continue;

            log.debug("处理第{}行: {}", i + 1, line);

            try {
                // 检测题目开始 - 精确匹配实际格式
                // 格式：数字、[题目类型]题目内容
                boolean isQuestionStart = line.matches("^\\d+、\\[.*题\\].*");

                if (isQuestionStart) {
                    log.info("检测到题目行: [{}]", line);

                    // 保存上一题（如果存在且有效）
                    if (currentQuestion != null) {
                        if (isValidQuestion(currentQuestion)) {
                            questions.add(currentQuestion);
                            processedQuestions++;
                            log.info("保存题目: {}", currentQuestion.get("content"));
                        } else {
                            String error = String.format("题目 %d 格式不完整，已跳过", processedQuestions + 1);
                            log.warn(error);
                            errors.add(error);
                        }
                    }

                    // 开始新题
                    currentQuestion = new HashMap<>();
                    currentQuestion.put("options", new ArrayList<Map<String, String>>());

                    // 解析题目类型
                    if (line.contains("[判断题]")) {
                        currentType = "judgment";  // 使用前端期望的值
                        currentQuestion.put("type", "judgment");
                        currentQuestion.put("typeName", "判断题");
                        currentQuestion.put("questionType", "judgment");  // 前端期望的字段名
                        log.info("识别到判断题: {}", line);
                    } else if (line.contains("[单选题]")) {
                        currentType = "single";
                        currentQuestion.put("type", "single");
                        currentQuestion.put("typeName", "单选题");
                        currentQuestion.put("questionType", "single");  // 前端期望的字段名
                        log.info("识别到单选题: {}", line);
                    } else if (line.contains("[多选题]")) {
                        currentType = "multiple";
                        currentQuestion.put("type", "multiple");
                        currentQuestion.put("typeName", "多选题");
                        currentQuestion.put("questionType", "multiple");  // 前端期望的字段名
                        log.info("识别到多选题: {}", line);
                    } else {
                        // 默认为判断题
                        currentType = "judgment";  // 使用前端期望的值
                        currentQuestion.put("type", "judgment");
                        currentQuestion.put("typeName", "判断题");
                        currentQuestion.put("questionType", "judgment");  // 前端期望的字段名
                        log.info("未识别到明确类型，默认为判断题: {}", line);
                    }

                    // 提取题目内容 - 精确提取
                    // 格式：1、[判断题]CPU的主频很大程度上决定了计算机的性能。
                    String questionText = "";
                    int bracketEnd = line.indexOf("]");
                    if (bracketEnd != -1 && bracketEnd < line.length() - 1) {
                        questionText = line.substring(bracketEnd + 1).trim();
                    }

                    // 如果题目内容为空，记录错误但继续处理
                    if (questionText.isEmpty()) {
                        String error = String.format("第 %d 行题目内容为空: %s", i + 1, line);
                        log.warn(error);
                        errors.add(error);
                        currentQuestion = null;
                        continue;
                    }

                    log.info("提取到题目内容: [{}]", questionText);

                    currentQuestion.put("content", questionText);
                    currentQuestion.put("questionContent", questionText); // 前端需要的字段名
                    currentQuestion.put("difficulty", ""); // 预览时不设置默认难度，只有在保存时才设置
                    currentQuestion.put("explanation", ""); // 默认解析为空
                    currentQuestion.put("collapsed", false);

                    log.info("开始新题目: {} - {}", currentType, questionText);

                } else if (line.matches("^[A-Z].*")) {
                    // 选项 - 简化匹配，A开头的行
                    log.info("检测到可能的选项: [{}]", line);
                    if (currentQuestion != null && ("single".equals(currentType) || "multiple".equals(currentType) || "unknown".equals(currentType))) {

                        // 如果是未知类型，根据选项推断为单选题
                        if ("unknown".equals(currentType)) {
                            currentType = "single";
                            currentQuestion.put("type", "single");
                            currentQuestion.put("typeName", "单选题");
                            currentQuestion.put("questionType", "single");
                            log.info("根据选项推断题目类型为单选题");
                        }
                        @SuppressWarnings("unchecked")
                        List<Map<String, String>> options = (List<Map<String, String>>) currentQuestion.get("options");
                        Map<String, String> option = new HashMap<>();
                        String optionKey = line.substring(0, 1);
                        String optionContent = "";

                        // 提取选项内容，支持多种分隔符
                        if (line.length() > 2) {
                            if (line.charAt(1) == '、') {
                                optionContent = line.substring(2).trim();
                            } else if (line.charAt(1) == '.' || line.charAt(1) == '：' || line.charAt(1) == ':') {
                                optionContent = line.substring(2).trim();
                            } else {
                                optionContent = line.substring(1).trim();
                            }
                        }

                        option.put("optionKey", optionKey); // 前端期望的字段名
                        option.put("optionContent", optionContent); // 前端期望的字段名
                        option.put("label", optionKey); // 保留原字段名兼容
                        option.put("content", optionContent); // 保留原字段名兼容
                        options.add(option);
                        log.debug("添加选项: {}", line);
                    }
                } else if (line.startsWith("答案：")) {
                    // 答案 - 精确匹配格式：答案：正确
                    log.info("检测到答案: [{}]", line);
                    if (currentQuestion != null) {
                        // 提取答案：答案：正确 -> 正确
                        String answer = line.substring(3).trim(); // "答案：".length() = 3

                        // 处理答案
                        if ("judgment".equals(currentType)) {
                            String judgeAnswer = ("正确".equals(answer) || "对".equals(answer)) ? "1" : "0";
                            currentQuestion.put("answer", judgeAnswer);
                            currentQuestion.put("correctAnswer", answer); // 前端期望的字段名，显示原始答案
                        } else {
                            currentQuestion.put("answer", answer);
                            currentQuestion.put("correctAnswer", answer); // 前端期望的字段名
                        }
                        log.debug("设置答案: {}", answer);
                    }
                } else if (line.startsWith("难度：") || line.startsWith("难度:")) {
                    // 解析难度信息
                    if (currentQuestion != null) {
                        String difficulty = line.substring(3).trim(); // "难度：".length() = 3

                        // 标准化难度值
                        if ("中".equals(difficulty)) {
                            difficulty = "中等";
                        }

                        // 只接受标准的三个难度级别
                        if ("简单".equals(difficulty) || "中等".equals(difficulty) || "困难".equals(difficulty)) {
                            currentQuestion.put("difficulty", difficulty);
                            log.debug("设置难度: {}", difficulty);
                        } else {
                            log.warn("不支持的难度级别: {}, 已忽略", difficulty);
                        }
                    }
                } else {
                    // 无法识别的行，记录但不中断解析
                    skippedLines++;
                    log.debug("跳过无法识别的行 {}: [{}]", i + 1, line);
                }
            } catch (Exception e) {
                // 单行解析错误，记录错误但继续处理下一行
                String error = String.format("第 %d 行解析出错: %s - %s", i + 1, line, e.getMessage());
                log.error(error, e);
                errors.add(error);
                skippedLines++;
            }
        }

        // 添加最后一题（如果存在且有效）
        if (currentQuestion != null) {
            if (isValidQuestion(currentQuestion)) {
                questions.add(currentQuestion);
                processedQuestions++;
                log.info("保存最后一题: {}", currentQuestion.get("content"));
            } else {
                String error = String.format("最后一题格式不完整，已跳过");
                log.warn(error);
                errors.add(error);
            }
        }

        log.info("题目解析完成，共解析出 {} 道题目，错误 {} 个，跳过 {} 行", questions.size(), errors.size(), skippedLines);

        // 如果没有解析出任何题目，添加详细的错误信息
        if (questions.isEmpty()) {
            errors.add("未能解析出任何有效题目，请检查文档格式是否正确");
            errors.add("题目格式应为：数字、[题目类型]题目内容");
            errors.add("例如：1、[判断题]这是一道判断题");
        }

        return questions;
    }

    /**
     * 验证题目是否有效
     */
    private boolean isValidQuestion(Map<String, Object> question) {
        if (question == null) {
            return false;
        }

        // 检查必要字段
        String content = (String) question.get("content");
        String type = (String) question.get("type");

        if (content == null || content.trim().isEmpty()) {
            log.warn("题目内容为空");
            return false;
        }

        if (type == null || type.trim().isEmpty()) {
            log.warn("题目类型为空");
            return false;
        }

        // 对于选择题，检查是否有选项
        if (("single".equals(type) || "multiple".equals(type))) {
            @SuppressWarnings("unchecked")
            List<Map<String, String>> options = (List<Map<String, String>>) question.get("options");
            if (options == null || options.isEmpty()) {
                log.warn("选择题缺少选项: {}", content);
                return false;
            }
        }

        return true;
    }

    /**
     * 提取文档标题
     */
    private String extractDocumentTitle(String content) {
        String[] lines = content.split("\\n");
        for (String line : lines) {
            line = line.trim();
            // 查找第一行非空内容作为标题，且不包含题目标识
            if (!line.isEmpty() && !line.matches(".*\\d+、\\[.*题\\].*")) {
                return line;
            }
        }
        return "题目文档"; // 默认标题
    }

    /**
     * 生成格式化的HTML内容
     */
    private String generateFormattedHtml(List<Map<String, Object>> questions, String documentTitle) {
        if (questions == null || questions.isEmpty()) {
            return "<h2 style='text-align: center; color: #333;'>" + documentTitle + "</h2><p>未解析出任何题目内容</p>";
        }

        StringBuilder html = new StringBuilder();

        // 添加文档标题
        html.append("<h2 style='text-align: center; color: #333; margin-bottom: 20px;'>")
            .append(documentTitle)
            .append("</h2>");

        for (int i = 0; i < questions.size(); i++) {
            Map<String, Object> question = questions.get(i);
            String type = (String) question.get("type");
            String typeName = (String) question.get("typeName");
            String content = (String) question.get("content");
            String answer = (String) question.get("answer");
            @SuppressWarnings("unchecked")
            List<Map<String, String>> options = (List<Map<String, String>>) question.get("options");

            // 题目编号和类型
            html.append("<p><strong>").append(i + 1).append("、[").append(typeName).append("]</strong>");

            // 题目内容
            html.append(content).append("</p>");

            // 选项（如果有）
            if (options != null && !options.isEmpty()) {
                html.append("<p>");
                for (Map<String, String> option : options) {
                    html.append("<br/>").append(option.get("label")).append("、").append(option.get("content"));
                }
                html.append("</p>");
            }

            // 答案
            html.append("<p><strong>答案：</strong>");
            if ("judgment".equals(type)) {
                // 对于判断题，直接显示原始答案文字
                String correctAnswer = (String) question.get("correctAnswer");
                html.append(correctAnswer != null ? correctAnswer : answer);
            } else {
                html.append(answer);
            }
            html.append("</p>");

            // 题目间距
            if (i < questions.size() - 1) {
                html.append("<br/>");
            }
        }

        return html.toString();
    }

    /**
     * 调试接口 - 查看文档原始内容
     */
    @PostMapping("/debugDocumentContent")
    public AjaxResult debugDocumentContent(@RequestParam("file") MultipartFile file)
    {
        try
        {
            log.info("调试文档内容，文件名: {}", file.getOriginalFilename());

            String content = "";
            String fileName = file.getOriginalFilename().toLowerCase();

            if (fileName.endsWith(".docx")) {
                // 解析Word文档
                try (XWPFDocument document = new XWPFDocument(file.getInputStream())) {
                    StringBuilder sb = new StringBuilder();
                    for (XWPFParagraph paragraph : document.getParagraphs()) {
                        String text = paragraph.getText();
                        if (text != null && !text.trim().isEmpty()) {
                            sb.append(text).append("\n");
                        }
                    }
                    content = sb.toString();
                }
            }

            // 按行分割并返回前50行
            String[] lines = content.split("\\n");
            List<String> debugLines = new ArrayList<>();
            for (int i = 0; i < Math.min(50, lines.length); i++) {
                debugLines.add(String.format("第%d行: [%s]", i + 1, lines[i]));
            }

            AjaxResult result = AjaxResult.success("调试信息获取成功");
            result.put("totalLines", lines.length);
            result.put("debugLines", debugLines);
            result.put("rawContent", content.substring(0, Math.min(2000, content.length())));

            return result;
        }
        catch (Exception e)
        {
            log.error("调试文档内容失败", e);
            return AjaxResult.error("调试失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入题目
     */
    @PostMapping("/batchImportQuestions")
    public AjaxResult batchImportQuestions(@RequestBody Map<String, Object> importData)
    {
        try
        {
            Long bankId = Long.valueOf(importData.get("bankId").toString());
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> questions = (List<Map<String, Object>>) importData.get("questions");
            Boolean allowDuplicate = (Boolean) importData.getOrDefault("allowDuplicate", false);
            Boolean reverse = (Boolean) importData.getOrDefault("reverse", false);

            log.info("批量导入题目，题库ID: {}, 题目数量: {}, 允许重复: {}, 倒序导入: {}",
                    bankId, questions.size(), allowDuplicate, reverse);

            int successCount = 0;
            int failCount = 0;
            List<String> errors = new ArrayList<>();
            Set<String> existingQuestions = new HashSet<>();

            // 如果不允许重复，先获取现有题目内容用于去重
            if (!allowDuplicate) {
                Question queryQuestion = new Question();
                queryQuestion.setBankId(bankId);
                List<Question> existingQuestionList = questionService.selectQuestionList(queryQuestion);
                for (Question q : existingQuestionList) {
                    if (q.getQuestionContent() != null) {
                        String normalizedContent = q.getQuestionContent().trim().replaceAll("\\s+", " ");
                        existingQuestions.add(normalizedContent);
                    }
                }
                log.info("启用去重功能，已加载 {} 个现有题目用于去重检查", existingQuestions.size());
            }

            // 处理题目导入（题目已经在前端按需求排序）
            for (int i = 0; i < questions.size(); i++) {
                Map<String, Object> questionData = questions.get(i);
                try {
                    String questionContent = (String) questionData.get("questionContent");
                    if (questionContent == null) {
                        questionContent = (String) questionData.get("content");
                    }

                    // 检查重复
                    if (!allowDuplicate && questionContent != null) {
                        String normalizedContent = questionContent.trim().replaceAll("\\s+", " ");
                        if (existingQuestions.contains(normalizedContent)) {
                            log.debug("跳过重复题目: {}", questionContent.substring(0, Math.min(50, questionContent.length())));
                            continue;
                        }
                        existingQuestions.add(normalizedContent);
                    }

                    // 转换题目数据为QuestionDTO
                    QuestionDTO questionDTO = convertMapToQuestionDTO(questionData, bankId);

                    // 验证题目数据
                    questionConverter.validateQuestionData(questionDTO);

                    // 转换DTO为实体
                    Question question = questionConverter.dtoToEntity(questionDTO);

                    // 保存题目到数据库
                    int result = questionService.insertQuestion(question);

                    if (result > 0) {
                        successCount++;
                        log.debug("导入题目成功 [{}]: {}", i + 1,
                                questionContent != null ? questionContent.substring(0, Math.min(50, questionContent.length())) : "无内容");
                    } else {
                        throw new Exception("数据库保存失败");
                    }

                    // 模拟处理时间
                    if (i % 10 == 0) {
                        Thread.sleep(10); // 每10题暂停10ms，模拟真实处理时间
                    }
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "第" + (i + 1) + "题导入失败: " + e.getMessage();
                    errors.add(errorMsg);
                    log.error("题目导入失败", e);
                }
            }

            String message = String.format("批量导入完成，成功: %d, 失败: %d", successCount, failCount);
            log.info(message);

            AjaxResult result = AjaxResult.success(message);
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("successCount", successCount);
            resultData.put("failCount", failCount);
            resultData.put("errors", errors);
            resultData.put("totalCount", questions.size());
            resultData.put("skippedCount", questions.size() - successCount - failCount);
            result.put("data", resultData);

            return result;
        }
        catch (Exception e)
        {
            log.error("批量导入题目失败", e);
            return AjaxResult.error("批量导入失败: " + e.getMessage());
        }
    }

    /**
     * 将Map数据转换为QuestionDTO
     */
    private QuestionDTO convertMapToQuestionDTO(Map<String, Object> questionData, Long bankId) {
        QuestionDTO dto = new QuestionDTO();

        // 设置基本信息
        dto.setBankId(bankId);

        // 获取题目内容
        String questionContent = (String) questionData.get("questionContent");
        if (questionContent == null) {
            questionContent = (String) questionData.get("content");
        }
        dto.setQuestionContent(questionContent);

        // 获取题型
        String questionType = (String) questionData.get("questionType");
        if (questionType == null) {
            questionType = (String) questionData.get("type");
        }
        dto.setQuestionType(questionType);

        // 获取难度
        String difficulty = (String) questionData.get("difficulty");
        if (difficulty == null || difficulty.trim().isEmpty()) {
            difficulty = "中等"; // 默认难度
        }
        dto.setDifficulty(difficulty);

        // 获取解析
        String explanation = (String) questionData.get("explanation");
        if (explanation == null) {
            explanation = (String) questionData.get("analysis");
        }
        dto.setExplanation(explanation);

        // 获取正确答案
        String correctAnswer = (String) questionData.get("correctAnswer");
        dto.setCorrectAnswer(correctAnswer);

        // 处理选项
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> optionsList = (List<Map<String, Object>>) questionData.get("options");
        if (optionsList != null && !optionsList.isEmpty()) {
            List<QuestionDTO.OptionDTO> options = new ArrayList<>();
            for (Map<String, Object> optionMap : optionsList) {
                String optionKey = (String) optionMap.get("optionKey");
                if (optionKey == null) {
                    optionKey = (String) optionMap.get("key");
                }
                String optionContent = (String) optionMap.get("optionContent");
                if (optionContent == null) {
                    optionContent = (String) optionMap.get("content");
                }

                // 判断是否为正确答案
                Boolean isCorrect = false;
                if (correctAnswer != null && optionKey != null) {
                    if ("judgment".equals(questionType)) {
                        // 判断题特殊处理
                        if (("true".equals(correctAnswer) && "正确".equals(optionContent)) ||
                            ("false".equals(correctAnswer) && "错误".equals(optionContent))) {
                            isCorrect = true;
                        }
                    } else {
                        // 选择题处理
                        isCorrect = correctAnswer.contains(optionKey);
                    }
                }

                options.add(new QuestionDTO.OptionDTO(optionKey, optionContent, isCorrect));
            }
            dto.setOptions(options);
        } else if ("judgment".equals(questionType)) {
            // 判断题如果没有选项，创建默认的正确/错误选项
            List<QuestionDTO.OptionDTO> options = new ArrayList<>();
            boolean isTrue = "true".equals(correctAnswer) || "正确".equals(correctAnswer);
            options.add(new QuestionDTO.OptionDTO("A", "正确", isTrue));
            options.add(new QuestionDTO.OptionDTO("B", "错误", !isTrue));
            dto.setOptions(options);
        }

        return dto;
    }


}
