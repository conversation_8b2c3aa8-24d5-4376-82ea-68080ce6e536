<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <!-- 标题行 -->
      <div class="header-title">
        <el-button
          type="primary"
          icon="el-icon-back"
          @click="goBack"
          style="margin-right: 15px;"
        >
          返回题库列表
        </el-button>
        <h2 style="margin: 0; display: inline-block;">{{ bankName }}</h2>
      </div>

      <!-- 搜索和统计行 -->
      <div class="header-content">
        <!-- 搜索条件 -->
        <div class="search-section">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="题型" prop="questionType">
              <el-select v-model="queryParams.questionType" placeholder="请选择题型" clearable style="width: 120px;">
                <el-option label="单选题" value="single"></el-option>
                <el-option label="多选题" value="multiple"></el-option>
                <el-option label="判断题" value="judgment"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="难度" prop="difficulty">
              <el-select v-model="queryParams.difficulty" placeholder="请选择难度" clearable style="width: 100px;">
                <el-option label="简单" value="简单"></el-option>
                <el-option label="中等" value="中等"></el-option>
                <el-option label="困难" value="困难"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="题目内容" prop="questionContent">
              <el-input
                v-model="queryParams.questionContent"
                placeholder="请输入题干内容关键词"
                clearable
                style="width: 200px;"
                @keyup.enter.native="handleSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stats-container">
            <div class="stat-item">
              <span class="stat-label">总题数</span>
              <span class="stat-value">{{ statistics.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">单选题</span>
              <span class="stat-value">{{ statistics.singleChoice }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">多选题</span>
              <span class="stat-value">{{ statistics.multipleChoice }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">判断题</span>
              <span class="stat-value">{{ statistics.judgment }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="operation-left">
        <el-button
          type="success"
          icon="el-icon-upload2"
          @click="handleBatchImportClick"
        >
          批量导题
        </el-button>
        <el-dropdown @command="handleAddQuestion" style="margin-left: 10px;">
          <el-button type="primary">
            单个录入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="single">
              <i class="el-icon-circle-check" style="margin-right: 8px; color: #409eff;"></i>
              单选题
            </el-dropdown-item>
            <el-dropdown-item command="multiple">
              <i class="el-icon-finished" style="margin-right: 8px; color: #67c23a;"></i>
              多选题
            </el-dropdown-item>
            <el-dropdown-item command="judgment">
              <i class="el-icon-success" style="margin-right: 8px; color: #e6a23c;"></i>
              判断题
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <!-- 操作按钮组 -->
        <el-button-group style="margin-left: 10px;">
          <el-button
            icon="el-icon-download"
            @click="handleExportQuestions"
          >
            导出
          </el-button>
          <el-button
            :type="isAllSelected ? 'primary' : 'default'"
            :icon="isAllSelected ? 'el-icon-check' : 'el-icon-minus'"
            @click="handleToggleSelectAll"
          >
            {{ isAllSelected ? '全不选' : '全选' }}
          </el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleBatchDelete"
            :disabled="selectedQuestions.length === 0"
          >
            删除
          </el-button>
        </el-button-group>
      </div>
      <div class="operation-right">
        <el-button
          :type="expandAll ? 'warning' : 'info'"
          :icon="expandAll ? 'el-icon-minus' : 'el-icon-plus'"
          @click="toggleExpandAll"
        >
          {{ expandAll ? '收起所有题目' : '展开所有题目' }}
        </el-button>
      </div>
    </div>



    <!-- 题目列表 -->
    <div class="question-list">
      <div v-if="questionList.length === 0" class="empty-state">
        <el-empty description="暂无题目数据">
          <el-button type="primary" @click="handleAddQuestion('single')">添加第一道题目</el-button>
        </el-empty>
      </div>
      <div v-else>
        <question-card
          v-for="(question, index) in questionList"
          :key="question.questionId"
          :question="question"
          :index="index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize"
          :expanded="expandAll || expandedQuestions.includes(question.questionId)"
          :selected="selectedQuestions.includes(question.questionId)"
          @toggle-expand="handleToggleExpand"
          @edit="handleEditQuestion"
          @copy="handleCopyQuestion"
          @delete="handleDeleteQuestion"
          @selection-change="handleQuestionSelect"
        />
      </div>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getQuestionList"
    />

    <!-- 题目表单对话框 -->
    <question-form
      :visible.sync="questionFormVisible"
      :question-type="currentQuestionType"
      :question-data="currentQuestionData"
      :bank-id="bankId"
      @success="handleQuestionFormSuccess"
    />

    <!-- 批量导入题目抽屉 -->
    <el-drawer
      title="批量导入题目"
      :visible.sync="importDrawerVisible"
      direction="rtl"
      size="90%"
      :show-close="true"
      :before-close="handleDrawerClose"
      class="batch-import-drawer"
    >
      <div class="main el-row">
        <!-- 左侧编辑区域 -->
        <div class="col-left h100p el-col el-col-12">
          <div class="toolbar clearfix">
            <div class="fr">
              <el-button
                type="primary"
                size="mini"
                @click="showDocumentImportDialog"
              >
                <i class="el-icon-folder-add"></i>
                文档导入
              </el-button>
              <el-button
                type="primary"
                size="mini"
                @click="showRulesDialog"
              >
                <i class="el-icon-reading"></i>
                输入规范与范例
              </el-button>
            </div>
          </div>

          <div class="editor-wrapper">
            <div id="rich-editor" class="rich-editor-container"></div>
          </div>
        </div>

        <!-- 右侧解析结果区域 -->
        <div class="col-right h100p el-col el-col-12">
          <div class="checkarea">
            <div class="import-actions">
              <el-button
                type="success"
                size="mini"
                class="mr20"
                @click="confirmImport"
                :disabled="parsedQuestions.length === 0"
                :loading="importingQuestions"
              >
                <i class="el-icon-upload2"></i>
                {{ importingQuestions ? '正在导入...' : '导入题目' }}
              </el-button>

              <div class="import-options">
                <el-checkbox
                  v-model="importOptions.reverse"
                  :disabled="importingQuestions"
                >
                  <el-tooltip content="勾选后将按题目顺序倒序导入，即最后一题先导入" placement="top">
                    <span>按题目顺序倒序导入</span>
                  </el-tooltip>
                </el-checkbox>

                <el-checkbox
                  v-model="importOptions.allowDuplicate"
                  :disabled="importingQuestions"
                >
                  <el-tooltip content="勾选后允许导入重复的题目内容，否则会跳过重复题目" placement="top">
                    <span>允许题目重复</span>
                  </el-tooltip>
                </el-checkbox>
              </div>

              <div v-if="importingQuestions" class="import-progress">
                <el-progress
                  :percentage="importProgress"
                  :show-text="true"
                  :format="formatProgress"
                  status="success"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>

          <div class="preview-wrapper">
            <div class="preview-header" v-if="parsedQuestions.length > 0">
              <h4>题目预览 ({{ parsedQuestions.length }})</h4>
              <div class="preview-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="toggleAllQuestions"
                  class="toggle-all-btn"
                >
                  <i :class="allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ allExpanded ? '全部收起' : '全部展开' }}
                </el-button>

              </div>
            </div>
            <div class="preview-scroll-wrapper">
              <div v-if="parsedQuestions.length === 0" class="empty-result">
                <i class="el-icon-document"></i>
                <p>暂无解析结果</p>
                <p class="tip">请在左侧输入题目内容</p>
              </div>

              <div
                v-for="(question, index) in parsedQuestions"
                :key="index"
                class="el-card question-item is-hover-shadow"
              >
                <div class="el-card__body">
                  <div class="question-top-bar">
                    <div class="question-title">
                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>
                    </div>
                    <div class="question-toggle">
                      <el-button
                        type="text"
                        size="mini"
                        @click="toggleQuestion(index)"
                        class="toggle-btn"
                      >
                        <i :class="question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                      </el-button>
                    </div>
                  </div>

                  <!-- 题目内容始终显示 -->
                  <div class="question-content">
                    <!-- 只显示题干 -->
                    <div class="question-main-line">
                      <span class="display-latex rich-text" v-html="getFormattedQuestionContent(question)"></span>
                    </div>

                    <!-- 选项显示（如果有） -->
                    <div v-if="question.options && question.options.length > 0" class="question-options">
                      <div
                        v-for="option in question.options"
                        :key="option.optionKey"
                        class="option-item"
                      >
                        {{ option.optionKey }}. {{ option.optionContent }}
                      </div>
                    </div>
                  </div>

                  <!-- 答案、解析、难度可收起 -->
                  <div v-show="!question.collapsed" class="question-meta">

                    <div v-if="question.correctAnswer" class="question-answer">
                      答案：{{ question.correctAnswer }}
                    </div>

                    <div v-if="question.difficulty && question.difficulty.trim() !== ''" class="question-difficulty">
                      难度：{{ question.difficulty }}
                    </div>

                    <div v-if="question.explanation" class="question-explanation">
                      解析：{{ question.explanation }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 文档导入对话框 -->
    <el-dialog
      title="上传文档导入题目"
      :visible.sync="documentImportDialogVisible"
      width="700px"
      class="document-upload-dialog"
    >
      <div style="text-align: center;">
        <div class="subtitle" style="line-height: 3;">
          <i class="el-icon-info"></i>
          上传前请先下载模板，按照模板要求将内容录入到模板中。
        </div>

        <div style="padding: 14px;">
          <!-- <el-button
            type="success"
            size="small"
            plain
            @click="downloadExcelTemplate"
          >
            <i class="el-icon-download"></i>
            下载excel模板
          </el-button> -->

          <el-button
            type="success"
            size="small"
            plain
            @click="downloadWordTemplate"
          >
            <i class="el-icon-download"></i>
            下载Word模板
          </el-button>
        </div>

        <div>
          <el-upload
            ref="documentUpload"
            class="upload-demo"
            drag
            :action="uploadUrl"
            :headers="uploadHeaders"
            :data="uploadData"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :accept="'.docx,.xlsx'"
            :limit="1"
            :disabled="isUploading || isParsing"
          >
            <div v-if="!isUploading && !isParsing">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </div>
            <div v-else-if="isUploading" class="upload-loading">
              <i class="el-icon-loading"></i>
              <div class="el-upload__text">正在上传文件...</div>
            </div>
            <div v-else-if="isParsing" class="upload-loading">
              <i class="el-icon-loading"></i>
              <div class="el-upload__text">正在解析文档，请稍候...</div>
            </div>
          </el-upload>
        </div>

        <div style="padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;">
          <div style="margin-bottom: 6px; font-weight: 700;">说明</div>
          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>
          2. 题目数量过多、题目文件过大等情况建议分批导入<br>
          3. 需严格按照各题型格式要求编辑题目文件
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="documentImportDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 输入规范与范例对话框 -->
    <el-dialog
      title="输入规范与范例"
      :visible.sync="rulesDialogVisible"
      width="900px"
      class="rules-dialog"
    >
      <el-tabs v-model="activeRuleTab" class="rules-tabs">
        <!-- 输入范例标签页 -->
        <el-tab-pane label="输入范例" name="examples">
          <div class="example-content">
            <div class="example-item">
              <p><strong>[单选题]</strong></p>
              <p>1.（  ）是我国最早的诗歌总集，又称作"诗三百"。</p>
              <p>A.《左传》</p>
              <p>B.《离骚》</p>
              <p>C.《坛经》</p>
              <p>D.《诗经》</p>
              <p>答案：D</p>
              <p>解析：诗经是我国最早的诗歌总集。</p>
              <p>难度：中等</p>
            </div>

            <div class="example-item">
              <p><strong>[多选题]</strong></p>
              <p>2.中华人民共和国的成立，标志着（ ）。</p>
              <p>A.中国新民主主义革命取得了基本胜利</p>
              <p>B.中国现代史的开始</p>
              <p>C.半殖民地半封建社会的结束</p>
              <p>D.中国进入社会主义社会</p>
              <p>答案：ABC</p>
              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>
            </div>

            <div class="example-item">
              <p><strong>[判断题]</strong></p>
              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>
              <p>答案：错误</p>
              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>
            </div>
          </div>
        </el-tab-pane>

        <!-- 输入规范标签页 -->
        <el-tab-pane label="输入规范" name="rules">
          <div class="rules-content">
            <div class="rule-section">
              <p><strong>题号（必填）：</strong></p>
              <p>1、题与题之间需要换行；</p>
              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>
              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>
            </div>

            <div class="rule-section">
              <p><strong>选项（必填）：</strong></p>
              <p>1、题干和第一个选项之间需要换行；</p>
              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>
              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>
              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为":：、.．"其中之一；</p>
            </div>

            <div class="rule-section">
              <p><strong>答案（必填）：</strong></p>
              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>
              <p>2、显式标注格式（答案：），冒号可以替换为 ":：、"其中之一；</p>
              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>
            </div>

            <div class="rule-section">
              <p><strong>解析（不必填）：</strong></p>
              <p>1、解析格式（解析：），冒号可以替换为 ":：、"其中之一；</p>
            </div>

            <div class="rule-section">
              <p><strong>难度（不必填）：</strong></p>
              <p>1、难度格式（难度：），冒号可以替换为 ":：、"其中之一；</p>
              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="rulesDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="copyExampleToEditor">将范例复制到编辑区</el-button>
      </div>
    </el-dialog>

    <!-- 文档导入对话框 -->
    <batch-import
      :visible.sync="batchImportVisible"
      :bank-id="bankId"
      :default-mode="currentImportMode"
      @success="handleBatchImportSuccess"
    />
  </div>
</template>

<script>
import QuestionCard from './components/QuestionCard'
import QuestionForm from './components/QuestionForm'
import BatchImport from './components/BatchImport'
import { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'
import { batchImportQuestions } from '@/api/biz/questionBank'

export default {
  name: "QuestionBankDetail",
  components: {
    QuestionCard,
    QuestionForm,
    BatchImport
  },
  data() {
    return {
      // 题库信息
      bankId: null,
      bankName: '',
      // 统计数据
      statistics: {
        total: 0,
        singleChoice: 0,
        multipleChoice: 0,
        judgment: 0
      },
      // 题目列表
      questionList: [],
      // 分页参数
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bankId: null,
        questionType: null,
        difficulty: null,
        questionContent: null
      },
      // 展开状态
      expandAll: false,
      expandedQuestions: [],
      // 选择状态
      selectedQuestions: [],
      isAllSelected: false,
      // 表单相关
      questionFormVisible: false,
      currentQuestionType: 'single',
      currentQuestionData: null,
      // 批量导入
      importDrawerVisible: false,
      batchImportVisible: false,
      currentImportMode: 'excel',
      // 文档导入抽屉
      documentContent: '',
      documentHtmlContent: '',
      parsedQuestions: [],
      parseErrors: [],
      allExpanded: true,
      isSettingFromBackend: false,
      documentImportDialogVisible: false,
      rulesDialogVisible: false,
      activeRuleTab: 'examples',
      // 上传和解析状态
      isUploading: false,
      isParsing: false,
      importingQuestions: false,
      importProgress: 0,
      importOptions: {
        reverse: false,
        allowDuplicate: false
      },
      // 文件上传
      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',
      uploadHeaders: {
        Authorization: 'Bearer ' + this.$store.getters.token
      },
      uploadData: {},
      // 富文本编辑器
      richEditor: null,
      editorInitialized: false
    }
  },

  watch: {
    // 监听文档内容变化，自动解析
    documentContent: {
      handler(newVal) {
        // 如果是从后端设置内容，不触发前端解析
        if (this.isSettingFromBackend) {
          return
        }



        if (newVal && newVal.trim()) {
          this.debounceParseDocument()
        } else {
          this.parsedQuestions = []
          this.parseErrors = []
        }
      },
      immediate: false
    },
    // 监听抽屉打开状态
    importDrawerVisible: {
      handler(newVal) {
        if (newVal) {
          // 抽屉打开时清空所有内容并初始化编辑器
          this.clearImportContent()
          this.$nextTick(() => {
            this.initRichEditor()
          })
        } else {
          // 抽屉关闭时销毁编辑器
          if (this.richEditor) {
            this.richEditor.destroy()
            this.richEditor = null
            this.editorInitialized = false
          }
        }
      },
      immediate: false
    }
  },

  created() {
    this.initPage()
    // 创建防抖函数
    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)
    // 初始化上传数据
    this.uploadData = {
      bankId: this.bankId
    }
    this.uploadHeaders = {
      Authorization: 'Bearer ' + this.$store.getters.token
    }
  },

  mounted() {
    // 编辑器将在抽屉打开时初始化

  },

  beforeDestroy() {


    // 销毁富文本编辑器
    if (this.richEditor) {
      this.richEditor.destroy()
      this.richEditor = null
    }
  },
  methods: {
    // 初始化页面
    initPage() {
      const { bankId, bankName } = this.$route.query
      if (!bankId) {
        this.$message.error('缺少题库ID参数')
        this.goBack()
        return
      }
      this.bankId = bankId
      this.bankName = bankName || '题库详情'
      this.queryParams.bankId = bankId
      this.getQuestionList()
      this.getStatistics()
    },
    // 返回题库列表
    goBack() {
      this.$router.back()
    },
    // 获取题目列表
    getQuestionList() {
      // 转换查询参数格式
      const params = this.convertQueryParams(this.queryParams)
      listQuestion(params).then(response => {
        this.questionList = response.rows
        this.total = response.total
      }).catch(error => {

        this.$message.error('获取题目列表失败')
      })
    },

    // 转换查询参数格式
    convertQueryParams(params) {
      const convertedParams = { ...params }

      // 转换题型
      if (convertedParams.questionType) {
        const typeMap = {
          'single': 1,
          'multiple': 2,
          'judgment': 3
        }
        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType
      }

      // 转换难度
      if (convertedParams.difficulty) {
        const difficultyMap = {
          '简单': 1,
          '中等': 2,
          '困难': 3
        }
        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty
      }

      // 清理空值
      Object.keys(convertedParams).forEach(key => {
        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {
          delete convertedParams[key]
        }
      })

      return convertedParams
    },
    // 获取统计数据
    getStatistics() {
      getQuestionStatistics(this.bankId).then(response => {
        this.statistics = response.data
      }).catch(error => {

        // 使用模拟数据
        this.statistics = {
          total: 0,
          singleChoice: 0,
          multipleChoice: 0,
          judgment: 0
        }
      })
    },


    // 处理批量导题按钮点击
    handleBatchImportClick() {
      this.importDrawerVisible = true
    },
    // 添加题目
    handleAddQuestion(type) {
      this.currentQuestionType = type
      this.currentQuestionData = null
      this.questionFormVisible = true
    },
    // 切换展开状态
    toggleExpandAll() {
      this.expandAll = !this.expandAll
      if (!this.expandAll) {
        this.expandedQuestions = []
      }
    },



    // 导出题目
    handleExportQuestions() {
      if (this.selectedQuestions.length === 0) {
        this.$message.warning('请先选择要导出的题目')
        return
      }
      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)
      // TODO: 实现导出功能
    },

    // 切换全选/全不选
    handleToggleSelectAll() {
      this.isAllSelected = !this.isAllSelected
      if (this.isAllSelected) {
        // 全选
        this.selectedQuestions = this.questionList.map(q => q.questionId)
        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)
      } else {
        // 全不选
        this.selectedQuestions = []
        this.$message.success('已取消选择所有题目')
      }
    },



    // 批量删除
    handleBatchDelete() {
      if (this.selectedQuestions.length === 0) {
        this.$message.warning('请先选择要删除的题目')
        return
      }

      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const deletePromises = this.selectedQuestions.map(questionId =>
          delQuestion(questionId)
        )

        Promise.all(deletePromises).then(() => {
          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)
          this.selectedQuestions = []
          this.isAllSelected = false
          this.getQuestionList()
          this.getStatistics()
        }).catch(error => {
          this.$message.error('批量删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 题目选择状态变化
    handleQuestionSelect(questionId, selected) {
      if (selected) {
        if (!this.selectedQuestions.includes(questionId)) {
          this.selectedQuestions.push(questionId)
        }
      } else {
        const index = this.selectedQuestions.indexOf(questionId)
        if (index > -1) {
          this.selectedQuestions.splice(index, 1)
        }
      }

      // 更新全选状态
      this.isAllSelected = this.selectedQuestions.length === this.questionList.length
    },
    // 切换单个题目展开状态
    handleToggleExpand(questionId) {
      const index = this.expandedQuestions.indexOf(questionId)
      if (index > -1) {
        // 收起题目
        this.expandedQuestions.splice(index, 1)
        // 如果当前是"展开所有"状态，则取消"展开所有"状态
        if (this.expandAll) {
          this.expandAll = false
          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目
          this.questionList.forEach(question => {
            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {
              this.expandedQuestions.push(question.questionId)
            }
          })
        }
      } else {
        // 展开题目
        this.expandedQuestions.push(questionId)
      }
    },
    // 编辑题目
    handleEditQuestion(question) {
      this.currentQuestionData = question
      this.currentQuestionType = question.questionType
      this.questionFormVisible = true
    },
    // 复制题目
    handleCopyQuestion(question) {
      // 创建复制的题目数据（移除ID相关字段）
      const copiedQuestion = {
        ...question,
        questionId: null,  // 清除ID，表示新增
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      }

      // 设置为编辑模式并打开表单
      this.currentQuestionData = copiedQuestion
      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)
      this.questionFormVisible = true
    },

    // 题型数字转字符串（用于复制功能）
    convertQuestionTypeToString(type) {
      const typeMap = {
        1: 'single',
        2: 'multiple',
        3: 'judgment'
      }
      return typeMap[type] || type
    },
    // 删除题目
    handleDeleteQuestion(question) {
      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')
      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent
      this.$confirm(`确认删除题目"${displayContent}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delQuestion(question.questionId).then(() => {
          this.$message.success('删除成功')
          this.getQuestionList()
          this.getStatistics()
        }).catch(error => {

          this.$message.error('删除题目失败')
        })
      })
    },
    // 题目表单成功回调
    handleQuestionFormSuccess() {
      this.questionFormVisible = false
      this.getQuestionList()
      this.getStatistics()
    },
    // 批量导入成功回调
    handleBatchImportSuccess() {
      this.batchImportVisible = false
      this.importDrawerVisible = false
      this.getQuestionList()
      this.getStatistics()
    },



    // 抽屉关闭前处理
    handleDrawerClose(done) {
      // 检查是否有未保存的内容
      const hasContent = this.documentContent && this.documentContent.trim().length > 0
      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0

      if (hasContent || hasParsedQuestions) {
        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'
        if (hasParsedQuestions) {
          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`
        }

        this.$confirm(message, '确认关闭', {
          confirmButtonText: '确定关闭',
          cancelButtonText: '继续编辑',
          type: 'warning'
        }).then(() => {
          // 清空内容
          this.clearImportContent()
          done()
        }).catch(() => {
          // 取消关闭，继续编辑
        })
      } else {
        // 没有内容直接关闭
        done()
      }
    },

    // 清空导入内容
    clearImportContent() {
      // 清空文档内容
      this.documentContent = ''
      this.documentHtmlContent = ''

      // 清空解析结果
      this.parsedQuestions = []
      this.parseErrors = []

      // 重置解析状态
      this.allExpanded = true
      this.isSettingFromBackend = false

      // 重置上传状态
      this.isUploading = false
      this.isParsing = false
      this.importingQuestions = false
      this.importProgress = 0

      // 重置导入选项
      this.importOptions = {
        reverse: false,
        allowDuplicate: false
      }
    },

    // 显示文档导入对话框
    showDocumentImportDialog() {
      // 清除上一次的上传状态和内容
      this.isUploading = false
      this.isParsing = false

      // 清除上传组件的文件列表
      this.$nextTick(() => {
        const uploadComponent = this.$refs.documentUpload
        if (uploadComponent) {
          uploadComponent.clearFiles()
        }
      })

      this.documentImportDialogVisible = true

    },

    // 显示规范对话框
    showRulesDialog() {
      this.activeRuleTab = 'examples' // 默认显示范例标签页
      this.rulesDialogVisible = true
    },

    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断
    copyExampleToEditor() {
      // 使用输入范例标签页里的前3题内容，转换为HTML格式
      const htmlTemplate = `
<p>1.（  ）是我国最早的诗歌总集，又称作"诗三百"。</p>
<p>A.《左传》</p>
<p>B.《离骚》</p>
<p>C.《坛经》</p>
<p>D.《诗经》</p>
<p>答案：D</p>
<p>解析：诗经是我国最早的诗歌总集。</p>
<p>难度：中等</p>
<p><br></p>

<p>2.中华人民共和国的成立，标志着（ ）。</p>
<p>A.中国新民主主义革命取得了基本胜利</p>
<p>B.中国现代史的开始</p>
<p>C.半殖民地半封建社会的结束</p>
<p>D.中国进入社会主义社会</p>
<p>答案：ABC</p>
<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>
<p><br></p>

<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>
<p>答案：错误</p>
<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>
      `.trim()

      // 直接设置到富文本编辑器
      if (this.richEditor && this.editorInitialized) {
        this.richEditor.setData(htmlTemplate)

      } else {
        // 如果编辑器未初始化，等待初始化后再设置
        this.$nextTick(() => {
          if (this.richEditor && this.editorInitialized) {
            this.richEditor.setData(htmlTemplate)

          }
        })
      }

      // 关闭对话框
      this.rulesDialogVisible = false

      // 提示用户
      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')


    },



    // 下载Word模板
    downloadWordTemplate() {
      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)
    },

    // 上传前检查
    beforeUpload(file) {


      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }

      // 更新上传数据
      this.uploadData.bankId = this.bankId

      // 设置上传状态
      this.isUploading = true
      this.isParsing = false



      return true
    },

    // 上传成功
    handleUploadSuccess(response, file) {


      if (response.code === 200) {
        // 上传完成，开始解析
        this.isUploading = false
        this.isParsing = true



        // 清除之前的解析结果，确保干净的开始
        this.parsedQuestions = []
        this.parseErrors = []

        // 延迟关闭对话框，让用户看到解析动画
        setTimeout(() => {
          this.documentImportDialogVisible = false
          this.isParsing = false
        }, 1500)

        // 设置标志位，避免触发前端重新解析
        this.isSettingFromBackend = true

        // 将解析结果显示在右侧
        if (response.questions && response.questions.length > 0) {
          this.parsedQuestions = response.questions.map(question => ({
            ...question,
            collapsed: false  // 默认展开
          }))
          // 重置全部展开状态
          this.allExpanded = true
          this.parseErrors = response.errors || []

          // 显示详细的解析结果
          const errorCount = response.errors ? response.errors.length : 0
          if (errorCount > 0) {
            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)
          } else {
            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)
          }


        } else {
          this.$message.error('未解析出任何题目，请检查文件格式')
          this.parsedQuestions = []
          this.parseErrors = response.errors || ['未能解析出题目内容']


        }

        // 将原始内容填充到富文本编辑器中
        if (response.originalContent) {
          this.setEditorContent(response.originalContent)
          this.documentContent = response.originalContent
          this.documentHtmlContent = response.originalContent // 初始化HTML内容

        }

        // 延迟重置标志位，确保所有异步操作完成
        setTimeout(() => {
          this.isSettingFromBackend = false
        }, 2000)
      } else {

        this.$message.error(response.msg || '文件上传失败')
        // 重置状态
        this.isUploading = false
        this.isParsing = false
      }
    },

    // 上传失败
    handleUploadError(error, file) {

      this.$message.error('文件上传失败，请检查网络连接或联系管理员')

      // 重置状态
      this.isUploading = false
      this.isParsing = false
    },



    // 切换题目展开/收起
    toggleQuestion(index) {
      const question = this.parsedQuestions[index]
      this.$set(question, 'collapsed', !question.collapsed)
    },

    // 全部展开/收起
    toggleAllQuestions() {
      this.allExpanded = !this.allExpanded
      this.parsedQuestions.forEach(question => {
        this.$set(question, 'collapsed', !this.allExpanded)
      })

    },

    // 确认导入
    confirmImport() {
      if (this.parsedQuestions.length === 0) {
        this.$message.warning('没有可导入的题目')
        return
      }

      // 构建确认信息
      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`
      let optionMessages = []

      if (this.importOptions.reverse) {
        optionMessages.push('将按倒序导入')
      }
      if (this.importOptions.allowDuplicate) {
        optionMessages.push('允许重复题目')
      }

      if (optionMessages.length > 0) {
        confirmMessage += `\n\n导入选项：${optionMessages.join('，')}`
      }

      this.$confirm(confirmMessage, '确认导入', {
        confirmButtonText: '确定导入',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: false
      }).then(() => {
        this.importQuestions()
      }).catch(() => {})
    },

    // 导入题目
    async importQuestions() {
      this.importingQuestions = true
      this.importProgress = 0

      try {
        // 处理导入选项
        let questionsToImport = [...this.parsedQuestions]

        if (this.importOptions.reverse) {
          questionsToImport.reverse()
          this.$message.info('已按倒序排列题目')
        }

        // 模拟进度更新
        this.importProgress = 10

        // 调用实际的导入API
        const importData = {
          bankId: this.bankId,
          questions: questionsToImport,
          allowDuplicate: this.importOptions.allowDuplicate,
          reverse: this.importOptions.reverse
        }

        this.importProgress = 30

        const response = await batchImportQuestions(importData)

        this.importProgress = 80

        if (response.code === 200) {
          this.importProgress = 100

          // 显示详细的导入结果
          const result = response.data || {}
          const successCount = result.successCount || questionsToImport.length
          const failCount = result.failCount || 0

          if (failCount > 0) {
            this.$message.warning(`导入完成：成功 ${successCount} 道，失败 ${failCount} 道题目`)
          } else {
            this.$message.success(`成功导入 ${successCount} 道题目`)
          }

          // 如果有错误信息，显示详情
          if (result.errors && result.errors.length > 0) {
            console.warn('导入错误详情:', result.errors)
          }
        } else {
          throw new Error(response.msg || '导入失败')
        }

        // 清理状态并关闭抽屉
        this.importDrawerVisible = false
        this.documentContent = ''
        this.documentHtmlContent = ''
        this.parsedQuestions = []
        this.parseErrors = []

        // 刷新数据
        this.getQuestionList()
        this.getStatistics()

      } catch (error) {
        console.error('导入题目失败:', error)
        this.$message.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        this.importingQuestions = false
        this.importProgress = 0
      }
    },

    // 格式化进度显示
    formatProgress(percentage) {
      if (percentage === 100) {
        return '导入完成'
      } else if (percentage >= 80) {
        return '正在保存...'
      } else if (percentage >= 30) {
        return '正在处理...'
      } else {
        return '准备中...'
      }
    },

    // 初始化富文本编辑器
    initRichEditor() {
      if (this.editorInitialized) {
        return
      }

      // 检查CKEditor是否可用
      if (!window.CKEDITOR) {

        this.fallbackToTextarea()
        return
      }

      try {
        // 如果编辑器已存在，先销毁
        if (this.richEditor) {
          this.richEditor.destroy()
          this.richEditor = null
        }

        // 确保容器存在
        const editorContainer = document.getElementById('rich-editor')
        if (!editorContainer) {

          return
        }

        // 创建textarea元素
        editorContainer.innerHTML = '<textarea id="rich-editor-textarea" name="rich-editor-textarea"></textarea>'

        // 等待DOM更新后创建编辑器
        this.$nextTick(() => {
          // 检查CKEditor是否可用
          if (!window.CKEDITOR || !window.CKEDITOR.replace) {

            this.showFallbackEditor = true
            return
          }

          try {
            // 先尝试完整配置
            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {
              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度
              toolbar: [
                { name: 'styles', items: ['FontSize'] },
                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },
                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },
                { name: 'colors', items: ['TextColor', 'BGColor'] },
                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },
                { name: 'editing', items: ['Undo', 'Redo'] },
                { name: 'links', items: ['Link', 'Unlink'] },
                { name: 'insert', items: ['Image', 'SpecialChar'] },
                { name: 'tools', items: ['Maximize'] }
              ],
              removeButtons: '',
              language: 'zh-cn',
              removePlugins: 'elementspath',
              resize_enabled: false,
              extraPlugins: 'font,colorbutton,justify,specialchar,image',
              allowedContent: true,
              // 字体大小配置
              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',
              fontSize_defaultLabel: '14px',
              // 颜色配置
              colorButton_enableMore: true,
              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',
              // 图像上传配置 - 参考您提供的标准配置
              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',
              image_previewText: ' ',
              // 设置基础路径，让相对路径能正确解析到后端服务器
              baseHref: 'http://localhost:8802/',
              // 图像插入配置
              image_previewText: '预览区域',
              image_removeLinkByEmptyURL: true,
              // 隐藏不需要的标签页，只保留上传和图像信息
              removeDialogTabs: 'image:Link;image:advanced',
              on: {
                instanceReady: function(evt) {
                  const editor = evt.editor
                  editor.on('dialogShow', function(evt) {
                    const dialog = evt.data
                    if (dialog.getName() === 'image') {
                      setTimeout(() => {
                        const checkInterval = setInterval(() => {
                          try {
                            const urlField = dialog.getContentElement('info', 'txtUrl')
                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {
                              clearInterval(checkInterval)
                              dialog.selectPage('info')
                            }
                          } catch (e) {
                            // 忽略错误
                          }
                        }, 500)
                        setTimeout(() => clearInterval(checkInterval), 10000)
                      }, 1000)
                    }
                  })
                }
              }
            })
          } catch (error) {
            this.fallbackToTextarea()
            return
          }

          // 监听内容变化
          if (this.richEditor && this.richEditor.on) {
            this.richEditor.on('change', () => {
              this.handleEditorContentChange()
            })

            this.richEditor.on('key', () => {
              setTimeout(() => {
                this.handleEditorContentChange()
              }, 100)
            })

            this.richEditor.on('instanceReady', () => {
              this.editorInitialized = true
              this.richEditor.setData('')
            })
          }
        })

      } catch (error) {
        this.fallbackToTextarea()
      }
    },

    // 处理编辑器内容变化
    handleEditorContentChange() {
      const rawContent = this.richEditor.getData()
      const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)
      this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)
      this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)
    },

    // 回退到普通文本框
    fallbackToTextarea() {
      const editorContainer = document.getElementById('rich-editor')
      if (editorContainer) {
        const textarea = document.createElement('textarea')
        textarea.className = 'fallback-textarea'
        textarea.placeholder = '请在此处粘贴或输入题目内容...'
        textarea.value = '' // 确保文本框为空
        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: "Courier New", monospace; font-size: 14px; line-height: 1.6; resize: none;'

        // 监听内容变化
        textarea.addEventListener('input', (e) => {
          this.documentContent = e.target.value
          this.documentHtmlContent = e.target.value
        })

        editorContainer.innerHTML = ''
        editorContainer.appendChild(textarea)
        this.editorInitialized = true
      }
    },



    // 设置编辑器内容
    setEditorContent(content) {
      if (this.richEditor && this.editorInitialized) {
        this.richEditor.setData(content)
      } else {
        this.documentContent = content
        this.documentHtmlContent = content
      }
    },



    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 将编辑器内容中的完整URL转换为相对路径
    convertUrlsToRelative(content) {
      if (!content) return content

      // 匹配当前域名的完整URL并转换为相对路径
      const currentOrigin = window.location.origin
      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '(/[^"\'\\s>]*)', 'g')

      return content.replace(urlRegex, '$1')
    },

    // 解析文档
    parseDocument() {
      if (!this.documentContent.trim()) {
        this.parsedQuestions = []
        this.parseErrors = []
        return
      }

      try {
        const parseResult = this.parseQuestionContent(this.documentContent)
        this.parsedQuestions = parseResult.questions.map(question => ({
          ...question,
          collapsed: false
        }))
        this.parseErrors = parseResult.errors
      } catch (error) {
        this.parseErrors = ['解析失败：' + error.message]
        this.parsedQuestions = []
      }
    },

    // 解析题目内容 - 优化版本，更加健壮
    parseQuestionContent(content) {
      const questions = []
      const errors = []

      if (!content || typeof content !== 'string') {

        return { questions, errors: ['解析内容为空或格式不正确'] }
      }

      try {


        const textContent = this.stripHtmlTagsKeepImages(content)

        if (!textContent || textContent.trim().length === 0) {
          return { questions, errors: ['处理后的内容为空'] }
        }

        const lines = textContent.split('\n').map(line => line.trim()).filter(line => line.length > 0)

        if (lines.length === 0) {
          return { questions, errors: ['没有有效的内容行'] }
        }



        let currentQuestionLines = []
        let questionNumber = 0

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i]

          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]
          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)

          if (isQuestionStart) {
            // 如果之前有题目内容，先处理之前的题目
            if (currentQuestionLines.length > 0) {
              try {
                const questionText = currentQuestionLines.join('\n')
                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)
                if (parsedQuestion) {
                  questions.push(parsedQuestion)
                }
              } catch (error) {
                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)
              }
            }

            // 开始新题目
            currentQuestionLines = [line]
            questionNumber++
          } else {
            // 如果当前在处理题目中，添加到当前题目
            if (currentQuestionLines.length > 0) {
              currentQuestionLines.push(line)
            }
          }
        }

        // 处理最后一个题目
        if (currentQuestionLines.length > 0) {
          try {
            const questionText = currentQuestionLines.join('\n')
            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)
            if (parsedQuestion) {
              questions.push(parsedQuestion)
            }
          } catch (error) {
            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)
          }
        }

      } catch (error) {
        errors.push(`文档解析失败: ${error.message}`)
      }

      return { questions, errors }
    },

    // 判断是否为题目开始行 - 按照输入规范
    isQuestionStartLine(line) {
      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）
      // 匹配格式：数字 + 符号(:：、.．) + 可选空格
      // 例如：1. 1、 1： 1． 等
      return /^\d+[.:：．、]\s*/.test(line)
    },

    // 判断是否为题型标注开始行
    isQuestionTypeStart(line) {
      // 匹配格式：[题目类型]
      // 例如：[单选题] [多选题] [判断题] 等
      return /^\[.*?题\]/.test(line)
    },

    // 从行数组解析单个题目 - 按照输入规范
    parseQuestionFromLines(questionText) {
      const lines = questionText.split('\n').map(line => line.trim()).filter(line => line.length > 0)

      if (lines.length === 0) {
        throw new Error('题目内容为空')
      }

      let questionType = 'judgment' // 默认判断题
      let questionContent = ''
      let contentStartIndex = 0

      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        const typeMatch = line.match(/\[(.*?题)\]/)
        if (typeMatch) {
          const typeText = typeMatch[1]

          // 转换题目类型
          if (typeText.includes('判断')) {
            questionType = 'judgment'
          } else if (typeText.includes('单选')) {
            questionType = 'single'
          } else if (typeText.includes('多选')) {
            questionType = 'multiple'
          } else if (typeText.includes('填空')) {
            questionType = 'fill'
          } else if (typeText.includes('简答')) {
            questionType = 'essay'
          }

          // 如果题型标注和题目内容在同一行
          const remainingContent = line.replace(/\[.*?题\]/, '').trim()
          if (remainingContent) {
            questionContent = remainingContent
            contentStartIndex = i + 1
          } else {
            contentStartIndex = i + 1
          }
          break
        }
      }

      // 如果没有找到题型标注，从第一行开始解析
      if (contentStartIndex === 0) {
        contentStartIndex = 0
      }

      // 提取题目内容（从题号行开始）
      for (let i = contentStartIndex; i < lines.length; i++) {
        const line = lines[i]

        // 如果是题号行，提取题目内容（移除题号）
        if (this.isQuestionStartLine(line)) {
          // 移除题号，提取题目内容
          questionContent = line.replace(/^\d+[.:：．、]\s*/, '').trim()
          contentStartIndex = i + 1
          break
        } else if (!questionContent) {
          // 如果还没有题目内容，当前行就是题目内容
          questionContent = line
          contentStartIndex = i + 1
          break
        }
      }

      // 继续收集题目内容（直到遇到选项或答案）
      for (let i = contentStartIndex; i < lines.length; i++) {
        const line = lines[i]

        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容
        if (this.isOptionLine(line) || this.isAnswerLine(line) ||
            this.isExplanationLine(line) || this.isDifficultyLine(line)) {
          break
        }

        // 继续添加到题目内容，但要确保不包含题号
        let cleanLine = line
        // 如果这行还包含题号，移除它
        if (this.isQuestionStartLine(line)) {
          cleanLine = line.replace(/^\d+[.:：．、]\s*/, '').trim()
        }

        if (cleanLine) {
          if (questionContent) {
            questionContent += '\n' + cleanLine
          } else {
            questionContent = cleanLine
          }
        }
      }

      if (!questionContent) {
        throw new Error('无法提取题目内容')
      }

      // 最终清理：确保题目内容不包含题号
      let finalQuestionContent = questionContent.trim()
      // 使用更强的清理逻辑，多次清理确保彻底移除题号
      while (/^\s*\d+[.:：．、]/.test(finalQuestionContent)) {
        finalQuestionContent = finalQuestionContent.replace(/^\s*\d+[.:：．、]\s*/, '').trim()
      }

      // 额外清理：移除可能的HTML标签内的题号
      if (finalQuestionContent.includes('<')) {
        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)
      }

      const question = {
        questionType: questionType,
        type: questionType,
        typeName: this.getTypeDisplayName(questionType),
        questionContent: finalQuestionContent,
        content: finalQuestionContent,
        difficulty: '', // 不设置默认值
        explanation: '',
        options: [],
        correctAnswer: '',
        collapsed: false  // 默认展开
      }

      // 解析选项（对于选择题）
      const optionResult = this.parseOptionsFromLines(lines, 0)
      question.options = optionResult.options

      // 根据选项数量推断题目类型（如果之前没有明确标注）
      if (questionType === 'judgment' && question.options.length > 0) {
        // 如果有选项，推断为选择题
        questionType = 'single'  // 默认为单选题
        question.questionType = questionType
        question.type = questionType
        question.typeName = this.getTypeDisplayName(questionType)
      }

      // 解析答案、解析、难度
      this.parseQuestionMetaFromLines(lines, question)

      // 根据答案长度进一步推断选择题类型
      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {
        // 如果答案包含多个字母，推断为多选题
        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {
          questionType = 'multiple'
          question.questionType = questionType
          question.type = questionType
          question.typeName = this.getTypeDisplayName(questionType)
        }
      }

      // 最终清理：确保题目内容完全没有题号
      question.questionContent = this.removeQuestionNumber(question.questionContent)
      question.content = question.questionContent

      return question
    },

    // 判断是否为选项行 - 按照输入规范
    isOptionLine(line) {
      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为":：、.．"其中之一
      return /^[A-Za-z][.:：．、]\s*/.test(line)
    },

    // 判断是否为答案行 - 按照输入规范
    isAnswerLine(line) {
      // 规范：显式标注格式（答案：），冒号可以替换为 ":：、"其中之一
      return /^答案[.:：、]\s*/.test(line)
    },

    // 判断是否为解析行 - 按照输入规范
    isExplanationLine(line) {
      // 规范：解析格式（解析：），冒号可以替换为 ":：、"其中之一
      return /^解析[.:：、]\s*/.test(line)
    },

    // 判断是否为难度行 - 按照输入规范
    isDifficultyLine(line) {
      // 规范：难度格式（难度：），冒号可以替换为 ":：、"其中之一
      return /^难度[.:：、]\s*/.test(line)
    },

    // 获取题目类型显示名称
    getTypeDisplayName(type) {
      const typeMap = {
        'judgment': '判断题',
        'single': '单选题',
        'multiple': '多选题',
        'fill': '填空题',
        'essay': '简答题'
      }
      return typeMap[type] || '判断题'
    },

    // 处理图片路径，将相对路径转换为完整路径
    processImagePaths(content) {
      if (!content || typeof content !== 'string') {
        return ''
      }

      try {
        const processedContent = content.replace(/<img([^>]*?)src="([^"]*?)"([^>]*?)>/g, (match, before, src, after) => {
          if (!src) return match

          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {
            return match
          }

          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)
          return `<img${before}src="${fullSrc}"${after}>`
        })

        return processedContent
      } catch (error) {
        return content
      }
    },

    // 保留富文本格式用于预览显示
    preserveRichTextFormatting(content) {
      if (!content || typeof content !== 'string') {
        return ''
      }

      try {
        // 保留常用的富文本格式标签
        let processedContent = content
          // 转换相对路径的图片
          .replace(/<img([^>]*?)src="([^"]*?)"([^>]*?)>/gi, (match, before, src, after) => {
            if (!src.startsWith('http') && !src.startsWith('data:')) {
              const fullSrc = this.processImagePaths(src)
              return `<img${before}src="${fullSrc}"${after}>`
            }
            return match
          })
          // 保留段落结构
          .replace(/<p[^>]*>/gi, '<p>')
          .replace(/<\/p>/gi, '</p>')
          // 保留换行
          .replace(/<br\s*\/?>/gi, '<br>')
          // 清理多余的空白段落
          .replace(/<p>\s*<\/p>/gi, '')
          .replace(/(<p>[\s\n]*<\/p>)/gi, '')

        return processedContent.trim()
      } catch (error) {
        return content
      }
    },

    // 移除HTML标签但保留图片标签
    stripHtmlTagsKeepImages(content) {
      if (!content || typeof content !== 'string') {
        return ''
      }

      try {
        const images = []
        let imageIndex = 0
        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {
          images.push(match)
          return `\n__IMAGE_PLACEHOLDER_${imageIndex++}__\n`
        })

        let textContent = contentWithPlaceholders
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<\/p>/gi, '\n')
          .replace(/<p[^>]*>/gi, '\n')
          .replace(/<[^>]*>/g, '')
          .replace(/\n\s*\n/g, '\n')

        let finalContent = textContent
        images.forEach((img, index) => {
          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`
          if (finalContent.includes(placeholder)) {
            finalContent = finalContent.replace(placeholder, img)
          }
        })

        return finalContent.trim()
      } catch (error) {
        return content
      }
    },

    // 从行数组解析选项 - 按照输入规范
    parseOptionsFromLines(lines, startIndex) {
      const options = []

      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {
        return { options }
      }

      try {
        for (let i = startIndex; i < lines.length; i++) {
          const line = lines[i]

          if (!line || typeof line !== 'string') {
            continue
          }

          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为":：、.．"其中之一
          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\s*(.*)/)
          if (optionMatch) {
            const optionKey = optionMatch[1].toUpperCase()
            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''

            if (optionKey && optionContent) {
              options.push({
                optionKey: optionKey,
                label: optionKey,
                optionContent: optionContent,
                content: optionContent
              })
            }
          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {
            // 遇到答案、解析或难度行，停止解析选项
            break
          } else {
            // 规范：选项与选项之间，可以换行，也可以在同一行
            // 如果选项在同一行，选项之间至少需要有一个空格
            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\s*[^\s]+(?:\s+[A-Za-z][.:：．、]\s*[^\s]+)*)/g)
            if (multipleOptionsMatch) {
              // 处理同一行多个选项的情况
              const singleOptions = line.split(/\s+(?=[A-Za-z][.:：．、])/)
              for (const singleOption of singleOptions) {
                if (!singleOption) continue

                const match = singleOption.match(/^([A-Za-z])[.:：．、]\s*(.*)/)
                if (match) {
                  const optionKey = match[1].toUpperCase()
                  const optionContent = match[2] ? match[2].trim() : ''

                  if (optionKey && optionContent) {
                    options.push({
                      optionKey: optionKey,
                      label: optionKey,
                      optionContent: optionContent,
                      content: optionContent
                    })
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        // 忽略错误
      }

      return { options }
    },

    // 从行数组解析题目元信息 - 按照输入规范
    parseQuestionMetaFromLines(lines, question) {
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]

        // 规范：显式标注格式（答案：），冒号可以替换为 ":：、"其中之一
        const answerMatch = line.match(/^答案[.:：、]\s*(.+)/)
        if (answerMatch) {
          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)
          continue
        }

        // 规范：解析格式（解析：），冒号可以替换为 ":：、"其中之一
        const explanationMatch = line.match(/^解析[.:：、]\s*(.+)/)
        if (explanationMatch) {
          question.explanation = explanationMatch[1].trim()
          continue
        }

        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别
        const difficultyMatch = line.match(/^难度[.:：、]\s*(简单|中等|困难|中)/)
        if (difficultyMatch) {
          let difficulty = difficultyMatch[1]
          // 标准化难度值：将"中"统一为"中等"
          if (difficulty === '中') {
            difficulty = '中等'
          }
          // 只接受标准的三个难度级别
          if (['简单', '中等', '困难'].includes(difficulty)) {
            question.difficulty = difficulty
          }
          continue
        }
      }

      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准
      // 如果没有找到显式答案，尝试从题目内容中提取
      if (!question.correctAnswer) {
        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)
      }
    },

    // 从题干中提取答案 - 按照输入规范
    extractAnswerFromQuestionContent(questionContent, questionType) {
      if (!questionContent || typeof questionContent !== 'string') {
        return ''
      }

      try {
        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号
        const patterns = [
          /【([^】]+)】/g,    // 中文方括号
          /\[([^\]]+)\]/g,   // 英文方括号
          /（([^）]+)）/g,    // 中文圆括号
          /\(([^)]+)\)/g     // 英文圆括号
        ]

        for (const pattern of patterns) {
          const matches = questionContent.match(pattern)
          if (matches && matches.length > 0) {
            // 提取最后一个匹配项作为答案（通常答案在题目末尾）
            const lastMatch = matches[matches.length - 1]
            const answer = lastMatch.replace(/[【】\[\]（）()]/g, '').trim()

            if (answer) {
              return this.parseAnswerValue(answer, questionType)
            }
          }
        }
      } catch (error) {
          // 忽略错误
        }

      return ''
    },

    // 解析答案值
    parseAnswerValue(answerText, questionType) {
      if (!answerText || typeof answerText !== 'string') {
        return ''
      }

      try {
        const trimmedAnswer = answerText.trim()

        if (!trimmedAnswer) {
          return ''
        }

        if (questionType === 'judgment') {
          // 判断题答案处理 - 保持原始格式，不转换为true/false
          return trimmedAnswer
        } else {
          // 选择题答案处理
          return trimmedAnswer.toUpperCase()
        }
      } catch (error) {
          return answerText || ''
        }
    },





    // 获取格式化的题目内容（支持富文本格式）
    getFormattedQuestionContent(question) {
      if (!question || !question.questionContent) {
        return ''
      }

      let content = question.questionContent

      // 如果有HTML内容且包含富文本标签，优先使用HTML内容
      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {
        // 从HTML内容中提取对应的题目内容
        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)
        if (htmlContent) {
          content = htmlContent
        }
      }

      // 清理题号：确保题目内容不以数字+符号开头
      content = this.removeQuestionNumber(content)

      return this.processImagePaths(content)
    },

    // 获取题型名称
    getQuestionTypeName(type) {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'judgment': '判断题'
      }
      return typeMap[type] || '未知'
    },

    // 清理题目内容中的题号
    removeQuestionNumber(content) {
      if (!content || typeof content !== 'string') {
        return content
      }

      // 处理HTML内容
      if (content.includes('<')) {
        // 对于HTML内容，需要清理标签内的题号
        return content.replace(/<p[^>]*>(\s*\d+[.:：．、]\s*)(.*?)<\/p>/gi, '<p>$2</p>')
                     .replace(/^(\s*\d+[.:：．、]\s*)/, '') // 清理开头的题号
                     .replace(/>\s*\d+[.:：．、]\s*/g, '>') // 清理标签后的题号
      } else {
        // 对于纯文本内容，直接清理开头的题号
        return content.replace(/^\s*\d+[.:：．、]\s*/, '').trim()
      }
    },

    // 从HTML内容中提取对应的题目内容
    extractQuestionFromHtml(plainContent, htmlContent) {
      if (!plainContent || !htmlContent) {
        return plainContent
      }

      try {
        // 简单的匹配策略：查找包含题目内容的HTML段落
        const plainText = plainContent.replace(/^\d+[.:：．、]\s*/, '').trim()

        // 在HTML内容中查找包含这个文本的段落
        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\/p>/gi) || []

        for (const paragraph of paragraphs) {
          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()
          // 清理段落文本中的题号再进行匹配
          const cleanParagraphText = paragraphText.replace(/^\s*\d+[.:：．、]\s*/, '').trim()
          if (cleanParagraphText.includes(plainText.substring(0, 20))) {
            // 找到匹配的段落，返回HTML格式（但要清理题号）
            return this.removeQuestionNumber(paragraph)
          }
        }

        return plainContent
      } catch (error) {
        return plainContent
      }
    },


    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1
      this.getQuestionList()
    },
    // 重置搜索
    resetSearch() {
      this.queryParams.questionType = null
      this.queryParams.difficulty = null
      this.queryParams.questionContent = null
      this.queryParams.pageNum = 1
      this.getQuestionList()
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  min-height: 32px;
}

.search-section {
  flex: 1;
}

.search-section .el-form {
  margin-bottom: 0;
}

.search-section .el-form-item {
  margin-bottom: 0;
}

.stats-section {
  flex-shrink: 0;
  padding-top: 0;
  display: flex;
  align-items: center;
  height: 32px;
}

.stats-container {
  display: flex;
  gap: 20px;
  align-items: center;
  height: 32px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 100%;
}

.stat-label {
  font-size: 12px;
  color: #666;
  line-height: 1;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  line-height: 1;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 10px 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}



.question-list {
  min-height: 400px;
  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */
}



.empty-state {
  text-align: center;
  padding: 50px 0;
}

/* 批量导入抽屉样式 */
.batch-import-drawer .el-drawer__body {
  padding: 0;
  height: 100%;
}

.main {
  height: 100%;
  margin: 0;
}

.col-left, .col-right {
  height: 100%;
  padding: 0 5px;
}

.h100p {
  height: 100%;
}

.toolbar {
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar .orange {
  color: #e6a23c;
  font-size: 14px;
}

.toolbar .fr {
  display: flex;
  gap: 10px;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.fr {
  float: right;
}

.editor-wrapper {
  flex: 1;
  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */
  padding: 0;
}

.rich-editor-container {
  height: 100%;
  width: 100%;
}

.rich-editor-container .cke {
  height: 100% !important;
}

.rich-editor-container .cke_contents {
  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */
}

/* 工具栏样式优化 */
.rich-editor-container .cke_top {
  background: #f5f5f5 !important;
  border-bottom: 1px solid #ddd !important;
  padding: 6px !important;
}

.rich-editor-container .cke_toolbox {
  background: transparent !important;
}

.rich-editor-container .cke_toolbar {
  background: transparent !important;
  border: none !important;
  margin: 2px 4px !important;
  float: left !important;
}

.rich-editor-container .cke_button {
  background: transparent !important;
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  margin: 1px !important;
}

.rich-editor-container .cke_button:hover {
  background: #e6e6e6 !important;
  border-color: #ccc !important;
}

.rich-editor-container .cke_button_on {
  background: #d4edfd !important;
  border-color: #66afe9 !important;
}

/* 下拉菜单样式 */
.rich-editor-container .cke_combo {
  background: transparent !important;
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  margin: 1px !important;
}

.rich-editor-container .cke_combo:hover {
  background: #e6e6e6 !important;
  border-color: #ccc !important;
}

.rich-editor-container .cke_combo_button {
  background: transparent !important;
  border: none !important;
  padding: 4px 8px !important;
}

/* 工具栏分组样式 */
.rich-editor-container .cke_toolgroup {
  background: transparent !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  margin: 2px !important;
  padding: 1px !important;
}

/* 图像相关样式 */
.rich-editor-container img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  margin: 10px 0 !important;
}

.rich-editor-container .cke_dialog {
  z-index: 10000 !important;
}

.rich-editor-container .cke_dialog_background_cover {
  z-index: 9999 !important;
}

.fallback-textarea {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 0;
  resize: none;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  padding: 20px;
  outline: none;
}

.document-textarea {
  height: 100% !important;
}

.document-textarea .el-textarea__inner {
  height: 100% !important;
  border: none;
  border-radius: 0;
  resize: none;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  padding: 20px;
}

.checkarea {
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.checkarea .title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-right: 15px;
}

.checkarea .green {
  color: #67c23a;
  margin-right: 15px;
}

.checkarea .red {
  color: #f56c6c;
  margin-right: 15px;
}

.checkarea .mr20 {
  margin-right: 20px;
}

.preview-wrapper {
  flex: 1;
  height: calc(100% - 120px);
  overflow: hidden;
}

.preview-scroll-wrapper {
  height: 100%;
  overflow-y: auto;
  padding: 10px;
}

.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-result i {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-result .tip {
  font-size: 12px;
  color: #ccc;
}

.question-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.question-item .el-card__body {
  padding: 10px 20px 15px 20px;
}

.question-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.question-top-bar .left font {
  font-weight: bold;
  color: #333;
}

.question-content {
  margin-top: 10px;
}

/* 题干显示 */
.question-main-line {
  margin-bottom: 8px;
}

.question-main-line .display-latex {
  margin: 0;
}

.display-latex {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 富文本格式支持 */
.rich-text {
  /* 加粗 */
  font-weight: normal;
}

.rich-text strong,
.rich-text b {
  font-weight: bold;
  color: #2c3e50;
}

.rich-text em,
.rich-text i {
  font-style: italic;
  color: #34495e;
}

.rich-text u {
  text-decoration: underline;
}

.rich-text s,
.rich-text strike {
  text-decoration: line-through;
}

.rich-text p {
  margin: 8px 0;
  line-height: 1.6;
}

.rich-text br {
  line-height: 1.6;
}

/* 确保HTML内容正确显示 */
.rich-text * {
  max-width: 100%;
}

.rich-text {
  word-wrap: break-word;
}

.question-options {
  margin: 4px 0 8px 0;
}

.option-item {
  padding: 2px 0;
  padding-left: 10px;
  font-size: 13px;
  color: #666;
  line-height: 1.3;
}



/* 文档上传对话框样式 */
.document-upload-dialog .subtitle {
  color: #409eff;
  font-size: 14px;
  margin: 10px 0;
}

.document-upload-dialog .el-button--small {
  margin: 0 5px;
}

.document-upload-dialog .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.document-upload-dialog .el-upload-dragger:hover {
  border-color: #409eff;
}

.document-upload-dialog .el-upload-dragger .el-icon-upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 20px 0 16px;
  line-height: 50px;
}

.document-upload-dialog .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.document-upload-dialog .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

/* 上传加载动画样式 */
.upload-loading {
  padding: 40px 0;
  color: #409EFF;
}

.upload-loading .el-icon-loading {
  font-size: 28px;
  animation: rotating 2s linear infinite;
  margin-bottom: 10px;
}

.upload-loading .el-upload__text {
  color: #409EFF;
  font-size: 14px;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.rules-dialog .rules-content {
  max-height: 500px;
  overflow-y: auto;
}

.rules-content h3 {
  margin-top: 0;
  color: #333;
}

.rule-section {
  margin-bottom: 25px;
}

.rule-section h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.rule-section p {
  margin: 8px 0;
  line-height: 1.6;
  color: #666;
}

.rule-section code {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #e74c3c;
}

.rule-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.rule-section li {
  margin: 5px 0;
  color: #666;
}

.example-section {
  margin-top: 30px;
}

.example-section h4 {
  color: #67c23a;
  margin-bottom: 15px;
}

.example-code {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* 新的规范对话框样式 */
.rules-dialog .rules-tabs {
  margin-top: -20px;
}

.rules-dialog .example-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 10px;
}

.rules-dialog .example-item {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.rules-dialog .example-item p {
  margin: 5px 0;
  line-height: 1.6;
  color: #333;
}

.rules-dialog .example-item p:first-child {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.rules-dialog .rule-section p:first-child {
  color: #409eff;
  font-weight: bold;
  margin-bottom: 10px;
}

/* 预览头部样式 */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.preview-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.preview-actions {
  display: flex;
  align-items: center;
}

.toggle-all-btn {
  color: #409eff;
  font-size: 13px;
  padding: 5px 10px;
}

.toggle-all-btn:hover {
  color: #66b1ff;
}

.toggle-all-btn i {
  margin-right: 4px;
}

/* 导入选项样式 */
.import-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.import-options .el-checkbox {
  margin-right: 0;
  margin-bottom: 0;
}

.import-options .el-checkbox__label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.import-options .el-tooltip {
  cursor: help;
}

.import-progress {
  margin-top: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e1f5fe;
}

.import-progress .el-progress {
  margin-bottom: 0;
}

.import-progress .el-progress__text {
  font-size: 14px !important;
  font-weight: 500;
  color: #409eff;
}

/* 题目元信息样式 */
.question-meta {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.question-answer,
.question-explanation,
.question-difficulty {
  margin: 6px 0;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.question-answer {
  background: #e8f4fd;
  color: #0066cc;
  border-left: 3px solid #409eff;
}

.question-explanation {
  background: #f0f9ff;
  color: #666;
  border-left: 3px solid #67c23a;
}

.question-difficulty {
  background: #fef0e6;
  color: #e6a23c;
  border-left: 3px solid #e6a23c;
}

/* 预览滚动区域样式 */
.preview-scroll-wrapper {
  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */
}

/* 题目顶部栏样式 */
.question-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-title {
  flex: 1;
}

.question-toggle {
  flex-shrink: 0;
}

.toggle-btn {
  color: #909399;
  font-size: 16px;
  padding: 4px;
  min-width: auto;
}

.toggle-btn:hover {
  color: #409eff;
}

/* 导入操作区域样式 */
.import-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

/* 未保存更改指示器 */
.unsaved-indicator {
  color: #f56c6c;
  font-size: 18px;
  margin-left: 8px;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}
</style>
