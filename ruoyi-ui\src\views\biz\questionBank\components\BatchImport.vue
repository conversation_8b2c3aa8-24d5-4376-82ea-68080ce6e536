<template>
  <el-dialog
    title="批量导入题目"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
    append-to-body
  >
    <div class="import-container">
      <!-- 导入步骤 -->
      <el-steps :active="currentStep" finish-status="success" style="margin-bottom: 30px;">
        <el-step title="下载模板"></el-step>
        <el-step title="上传文件"></el-step>
        <el-step title="数据预览"></el-step>
        <el-step title="导入完成"></el-step>
      </el-steps>

      <!-- 步骤1: 选择导入方式 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="import-mode-section">
          <h3>第一步：选择导入方式</h3>

          <!-- 导入方式选择 -->
          <el-radio-group v-model="importMode" style="margin-bottom: 30px;">
            <el-radio label="excel">Excel模板导入</el-radio>
            <el-radio label="document">文档内容导入</el-radio>
          </el-radio-group>

          <!-- Excel模板导入 -->
          <div v-if="importMode === 'excel'" class="template-section">
            <h4>Excel模板导入</h4>
            <p>请先下载题目导入模板，按照模板格式填写题目数据</p>

            <div class="template-buttons">
              <el-button type="primary" icon="el-icon-download" @click="downloadTemplate('all')">
                下载题目导入模板
              </el-button>
            </div>

            <div class="template-tips">
              <h4>填写说明：</h4>
              <ul>
                <li>模板支持单选题、多选题、判断题混合导入</li>
                <li>题目内容：必填，支持HTML格式</li>
                <li>选项内容：选择题必填，选项A-H，每个选项单独一列</li>
                <li>正确答案：必填，单选题填写A/B/C等，多选题用逗号分隔如A,C，判断题填写true/false</li>
                <li>难度系数：选填，可填写"简单"、"中等"、"困难"</li>
                <li>题目解析：选填，支持HTML格式</li>
                <li>请确保Excel文件编码为UTF-8，避免中文乱码</li>
              </ul>
            </div>
          </div>

          <!-- 文档内容导入 -->
          <div v-if="importMode === 'document'" class="document-section">
            <h4>文档内容导入</h4>
            <p>支持上传包含题目内容的文档文件，系统将自动解析题目信息</p>

            <div class="document-format-tips">
              <h4>格式要求：</h4>
              <div class="format-rules">
                <div class="rule-item">
                  <h5>题型标注（必填）：</h5>
                  <p><code>[单选题]</code> <code>[多选题]</code> <code>[判断题]</code></p>
                </div>
                <div class="rule-item">
                  <h5>题号规则（必填）：</h5>
                  <p>题目前必须有题号，如：<code>1.</code> <code>2：</code> <code>3．</code></p>
                </div>
                <div class="rule-item">
                  <h5>选项格式（必填）：</h5>
                  <p><code>A.选项内容</code> <code>B：选项内容</code></p>
                </div>
                <div class="rule-item">
                  <h5>答案标注（必填）：</h5>
                  <p><code>答案：A</code> 或题干内 <code>【A】</code></p>
                </div>
                <div class="rule-item">
                  <h5>解析和难度（可选）：</h5>
                  <p><code>解析：解析内容</code> <code>难度：中等</code></p>
                </div>
              </div>
            </div>

            <div class="document-example">
              <h4>示例格式：</h4>
              <pre class="example-text">
[单选题]
1.（ ）是我国最早的诗歌总集。
A.《左传》
B.《离骚》
C.《坛经》
D.《诗经》
答案：D
解析：诗经是我国最早的诗歌总集。
难度：中等

[判断题]
2.元杂剧的四大悲剧包括郑光祖的《赵氏孤儿》。
答案：错误
解析：《赵氏孤儿》实为纪君祥所作。
              </pre>
            </div>
          </div>
        </div>

        <div class="step-actions">
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="upload-section">
          <h3>第二步：上传题目文件</h3>
          <p v-if="importMode === 'excel'">请选择填写好的Excel文件进行上传</p>
          <p v-if="importMode === 'document'">请选择包含题目内容的文档文件进行上传</p>

          <el-upload
            ref="fileUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :before-upload="beforeFileUpload"
            :show-file-list="false"
            :accept="importMode === 'excel' ? '.xlsx,.xls' : '.txt,.doc,.docx'"
            drag
          >
            <div class="upload-area">
              <i class="el-icon-upload"></i>
              <div class="upload-text">
                <p>将文件拖到此处，或<em>点击上传</em></p>
                <p class="upload-tip" v-if="importMode === 'excel'">
                  支持 .xlsx、.xls 格式文件，文件大小不超过10MB
                </p>
                <p class="upload-tip" v-if="importMode === 'document'">
                  支持 .txt、.doc、.docx 格式文件，文件大小不超过10MB
                </p>
              </div>
            </div>
          </el-upload>

          <div v-if="uploadedFile" class="uploaded-file">
            <el-alert
              :title="`已上传文件：${uploadedFile.name}`"
              type="success"
              :closable="false"
              show-icon
            />
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="parseFile" :disabled="!uploadedFile" :loading="parsing">
            解析文件
          </el-button>
        </div>
      </div>

      <!-- 步骤3: 数据预览 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="preview-section">
          <h3>第三步：数据预览与确认</h3>
          <p>共解析到 {{ parsedData.length }} 道题目，请确认数据无误后点击导入</p>

          <!-- 导入选项 -->
          <div class="import-options-section">
            <h4>导入选项</h4>
            <div class="import-options">
              <el-checkbox
                v-model="importOptions.reverse"
                :disabled="importing"
              >
                <el-tooltip content="勾选后将按题目顺序倒序导入，即最后一题先导入" placement="top">
                  <span>按题目顺序倒序导入</span>
                </el-tooltip>
              </el-checkbox>

              <el-checkbox
                v-model="importOptions.allowDuplicate"
                :disabled="importing"
              >
                <el-tooltip content="勾选后允许导入重复的题目内容，否则会跳过重复题目" placement="top">
                  <span>允许题目重复</span>
                </el-tooltip>
              </el-checkbox>
            </div>
          </div>

          <div v-if="parseErrors.length > 0" class="error-section">
            <el-alert
              title="数据解析错误"
              type="error"
              :closable="false"
              show-icon
              style="margin-bottom: 15px;"
            />
            <div class="error-list">
              <div v-for="(error, index) in parseErrors" :key="index" class="error-item">
                第{{ error.row }}行：{{ error.message }}
              </div>
            </div>
          </div>

          <div class="preview-table">
            <el-table :data="parsedData.slice(0, 10)" border style="width: 100%">
              <el-table-column prop="questionType" label="题型" width="80">
                <template slot-scope="scope">
                  <el-tag :type="getQuestionTypeColor(scope.row.questionType)" size="mini">
                    {{ getQuestionTypeName(scope.row.questionType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="questionContent" label="题目内容" min-width="200" show-overflow-tooltip />
              <el-table-column prop="correctAnswer" label="正确答案" width="100" />
              <el-table-column prop="difficulty" label="难度" width="80" />
            </el-table>
            <div v-if="parsedData.length > 10" class="table-tip">
              仅显示前10条数据，共{{ parsedData.length }}条
            </div>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button
            type="primary"
            @click="confirmImport"
            :disabled="parseErrors.length > 0 || parsedData.length === 0"
            :loading="importing"
          >
            <i class="el-icon-upload2"></i>
            {{ importing ? '正在导入...' : '确认导入' }}
          </el-button>
        </div>
      </div>

      <!-- 步骤4: 导入完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="result-section">
          <div class="result-icon">
            <i class="el-icon-success" style="font-size: 60px; color: #67c23a;"></i>
          </div>
          <h3>导入完成</h3>
          <div class="result-stats">
            <p>成功导入 <span class="success-count">{{ importResult.successCount }}</span> 道题目</p>
            <p v-if="importResult.failCount > 0">
              失败 <span class="fail-count">{{ importResult.failCount }}</span> 道题目
            </p>
          </div>
          
          <div v-if="importResult.errors.length > 0" class="import-errors">
            <el-collapse>
              <el-collapse-item title="查看失败详情" name="errors">
                <div v-for="(error, index) in importResult.errors" :key="index" class="error-detail">
                  第{{ error.row }}行：{{ error.message }}
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <div class="step-actions">
          <el-button type="primary" @click="handleComplete">完成</el-button>
          <el-button @click="resetImport">重新导入</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import { downloadTemplate, parseImportFile, batchImportQuestions } from '@/api/biz/question'
import { download } from '@/utils/request'

export default {
  name: "BatchImport",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bankId: {
      type: [String, Number],
      required: true
    },
    defaultMode: {
      type: String,
      default: 'excel' // excel 或 document
    }
  },
  data() {
    return {
      dialogVisible: false,
      currentStep: 0,
      importMode: 'excel', // 导入模式：excel 或 document
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      uploadedFile: null,
      parsing: false,
      importing: false,
      parsedData: [],
      parseErrors: [],
      importOptions: {
        reverse: false,
        allowDuplicate: false
      },
      importResult: {
        successCount: 0,
        failCount: 0,
        errors: []
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.resetImport()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    defaultMode(val) {
      if (val) {
        this.importMode = val
      }
    }
  },
  methods: {
    // 下载模板
    downloadTemplate(type) {
      const fileName = type === 'all' ? '题目导入模板.xlsx' : `${this.getQuestionTypeName(type)}导入模板.xlsx`
      download('/biz/question/downloadTemplate', { questionType: type }, fileName)
    },
    // 获取题型名称
    getQuestionTypeName(type) {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'judgment': '判断题'
      }
      return typeMap[type] || '未知题型'
    },
    // 获取题型颜色
    getQuestionTypeColor(type) {
      const colorMap = {
        'single': 'primary',
        'multiple': 'success',
        'judgment': 'warning'
      }
      return colorMap[type] || 'info'
    },
    // 下一步
    nextStep() {
      this.currentStep++
    },
    // 上一步
    prevStep() {
      this.currentStep--
    },
    // 文件上传前验证
    beforeFileUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10

      if (this.importMode === 'excel') {
        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       file.type === 'application/vnd.ms-excel'
        if (!isExcel) {
          this.$message.error('只能上传Excel文件!')
          return false
        }
      } else if (this.importMode === 'document') {
        const isDocument = file.type === 'text/plain' ||
                          file.type === 'application/msword' ||
                          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                          file.name.toLowerCase().endsWith('.txt') ||
                          file.name.toLowerCase().endsWith('.doc') ||
                          file.name.toLowerCase().endsWith('.docx')
        if (!isDocument) {
          this.$message.error('只能上传文档文件(.txt, .doc, .docx)!')
          return false
        }
      }

      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },
    // 文件上传成功
    handleFileSuccess(response, file) {
      if (response.code === 200) {
        this.uploadedFile = {
          name: file.name,
          url: response.url,
          fileName: response.fileName
        }
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },
    // 文件上传失败
    handleFileError() {
      this.$message.error('文件上传失败')
    },
    // 解析文件
    parseFile() {
      this.parsing = true

      if (this.importMode === 'excel') {
        // Excel文件解析
        const parseData = {
          fileName: this.uploadedFile.fileName,
          bankId: this.bankId
        }
        parseImportFile(parseData).then(response => {
          this.parsing = false
          this.parsedData = response.data.questions || []
          this.parseErrors = response.data.errors || []
          this.nextStep()
        }).catch(error => {
          this.parsing = false
          console.error('解析文件失败', error)
          this.$message.error('解析文件失败')
        })
      } else if (this.importMode === 'document') {
        // 文档内容解析
        this.parseDocumentContent()
      }
    },

    // 解析文档内容
    async parseDocumentContent() {
      try {
        // 读取文件内容
        const fileContent = await this.readFileContent(this.uploadedFile.url)

        // 解析题目
        const parseResult = this.parseQuestionContent(fileContent)

        this.parsing = false
        this.parsedData = parseResult.questions
        this.parseErrors = parseResult.errors
        this.nextStep()
      } catch (error) {
        this.parsing = false
        console.error('解析文档内容失败', error)
        this.$message.error('解析文档内容失败')
      }
    },

    // 读取文件内容
    readFileContent(fileUrl) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', process.env.VUE_APP_BASE_API + fileUrl, true)
        xhr.setRequestHeader('Authorization', 'Bearer ' + getToken())
        xhr.responseType = 'text'

        xhr.onload = function() {
          if (xhr.status === 200) {
            resolve(xhr.responseText)
          } else {
            reject(new Error('读取文件失败'))
          }
        }

        xhr.onerror = function() {
          reject(new Error('读取文件失败'))
        }

        xhr.send()
      })
    },

    // 解析题目内容
    parseQuestionContent(content) {
      const questions = []
      const errors = []

      try {
        // 按题型分割内容
        const sections = this.splitByQuestionType(content)

        sections.forEach((section, sectionIndex) => {
          try {
            const parsedQuestions = this.parseSectionQuestions(section)
            questions.push(...parsedQuestions)
          } catch (error) {
            errors.push(`第${sectionIndex + 1}个题型区域解析失败: ${error.message}`)
          }
        })

      } catch (error) {
        errors.push(`文档解析失败: ${error.message}`)
      }

      return { questions, errors }
    },

    // 按题型分割内容
    splitByQuestionType(content) {
      const sections = []
      const typeRegex = /\[(单选题|多选题|判断题)\]/g

      let lastIndex = 0
      let match
      let currentType = null

      while ((match = typeRegex.exec(content)) !== null) {
        if (currentType) {
          // 保存上一个区域
          sections.push({
            type: currentType,
            content: content.substring(lastIndex, match.index).trim()
          })
        }
        currentType = match[1]
        lastIndex = match.index + match[0].length
      }

      // 保存最后一个区域
      if (currentType) {
        sections.push({
          type: currentType,
          content: content.substring(lastIndex).trim()
        })
      }

      return sections
    },

    // 解析区域内的题目
    parseSectionQuestions(section) {
      const questions = []
      const questionType = this.convertQuestionType(section.type)

      // 按题号分割题目
      const questionBlocks = this.splitByQuestionNumber(section.content)

      questionBlocks.forEach((block, index) => {
        try {
          const question = this.parseQuestionBlock(block, questionType, index + 1)
          if (question) {
            questions.push(question)
          }
        } catch (error) {
          throw new Error(`第${index + 1}题解析失败: ${error.message}`)
        }
      })

      return questions
    },

    // 按题号分割题目
    splitByQuestionNumber(content) {
      const blocks = []
      const numberRegex = /^\s*(\d+)[.:：．]\s*/gm

      let lastIndex = 0
      let match

      while ((match = numberRegex.exec(content)) !== null) {
        if (lastIndex > 0) {
          // 保存上一题
          blocks.push(content.substring(lastIndex, match.index).trim())
        }
        lastIndex = match.index
      }

      // 保存最后一题
      if (lastIndex < content.length) {
        blocks.push(content.substring(lastIndex).trim())
      }

      return blocks.filter(block => block.length > 0)
    },

    // 解析单个题目块
    parseQuestionBlock(block, questionType, questionIndex) {
      const lines = block.split('\n').map(line => line.trim()).filter(line => line.length > 0)

      if (lines.length === 0) {
        throw new Error('题目内容为空')
      }

      // 提取题号和题干
      const firstLine = lines[0]
      const numberMatch = firstLine.match(/^\s*(\d+)[.:：．]\s*(.*)/)
      if (!numberMatch) {
        throw new Error('题号格式不正确')
      }

      let questionContent = numberMatch[2]
      let currentLineIndex = 1

      // 继续读取题干内容（直到遇到选项）
      while (currentLineIndex < lines.length) {
        const line = lines[currentLineIndex]
        if (this.isOptionLine(line)) {
          break
        }
        questionContent += '\n' + line
        currentLineIndex++
      }

      const question = {
        questionType: questionType,
        questionContent: questionContent.trim(),
        difficulty: '中等',
        explanation: '',
        options: [],
        correctAnswer: ''
      }

      // 解析选项（对于选择题）
      if (questionType !== 'judgment') {
        const optionResult = this.parseOptions(lines, currentLineIndex)
        question.options = optionResult.options
        currentLineIndex = optionResult.nextIndex
      }

      // 解析答案、解析、难度
      this.parseQuestionMeta(lines, currentLineIndex, question)

      return question
    },

    // 判断是否为选项行
    isOptionLine(line) {
      return /^[A-Za-z][.:：．]\s*/.test(line)
    },

    // 解析选项
    parseOptions(lines, startIndex) {
      const options = []
      let currentIndex = startIndex

      while (currentIndex < lines.length) {
        const line = lines[currentIndex]
        const optionMatch = line.match(/^([A-Za-z])[.:：．]\s*(.*)/)

        if (!optionMatch) {
          break
        }

        options.push({
          optionKey: optionMatch[1].toUpperCase(),
          optionContent: optionMatch[2].trim()
        })

        currentIndex++
      }

      return { options, nextIndex: currentIndex }
    },

    // 解析题目元信息（答案、解析、难度）
    parseQuestionMeta(lines, startIndex, question) {
      for (let i = startIndex; i < lines.length; i++) {
        const line = lines[i]

        // 解析答案
        const answerMatch = line.match(/^答案[：:]\s*(.+)/)
        if (answerMatch) {
          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)
          continue
        }

        // 解析解析
        const explanationMatch = line.match(/^解析[：:]\s*(.+)/)
        if (explanationMatch) {
          question.explanation = explanationMatch[1].trim()
          continue
        }

        // 解析难度
        const difficultyMatch = line.match(/^难度[：:]\s*(简单|中等|困难)/)
        if (difficultyMatch) {
          question.difficulty = difficultyMatch[1]
          continue
        }
      }

      // 如果没有显式答案，尝试从题干中提取
      if (!question.correctAnswer) {
        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)
      }
    },

    // 解析答案
    parseAnswer(answerText, questionType) {
      if (questionType === 'judgment') {
        // 判断题答案处理
        if (answerText.includes('正确') || answerText.includes('对') || answerText.toLowerCase().includes('true')) {
          return 'true'
        } else if (answerText.includes('错误') || answerText.includes('错') || answerText.includes('假') || answerText.toLowerCase().includes('false')) {
          return 'false'
        }
        return answerText.trim()
      } else {
        // 选择题答案处理
        return answerText.replace(/[,，\s]/g, '').toUpperCase()
      }
    },

    // 从题干中提取答案
    extractAnswerFromContent(content, questionType) {
      // 支持的括号类型
      const bracketPatterns = [
        /【([^】]+)】/g,
        /\[([^\]]+)\]/g,
        /（([^）]+)）/g,
        /\(([^)]+)\)/g
      ]

      for (const pattern of bracketPatterns) {
        const matches = [...content.matchAll(pattern)]
        if (matches.length > 0) {
          const answer = matches[matches.length - 1][1] // 取最后一个匹配
          return this.parseAnswer(answer, questionType)
        }
      }

      return ''
    },

    // 转换题型
    convertQuestionType(typeText) {
      const typeMap = {
        '单选题': 'single',
        '多选题': 'multiple',
        '判断题': 'judgment'
      }
      return typeMap[typeText] || 'single'
    },

    // 确认导入
    confirmImport() {
      if (this.parsedData.length === 0) {
        this.$message.warning('没有可导入的题目')
        return
      }

      // 构建确认信息
      let confirmMessage = `确认导入 ${this.parsedData.length} 道题目吗？`
      let optionMessages = []

      if (this.importOptions.reverse) {
        optionMessages.push('将按倒序导入')
      }
      if (this.importOptions.allowDuplicate) {
        optionMessages.push('允许重复题目')
      }

      if (optionMessages.length > 0) {
        confirmMessage += `\n\n导入选项：${optionMessages.join('，')}`
      }

      this.$confirm(confirmMessage, '确认导入', {
        confirmButtonText: '确定导入',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: false
      }).then(() => {
        this.importData()
      }).catch(() => {})
    },

    // 导入数据
    importData() {
      this.importing = true

      // 处理导入选项
      let questionsToImport = [...this.parsedData]

      if (this.importOptions.reverse) {
        questionsToImport.reverse()
        this.$message.info('已按倒序排列题目')
      }

      const importData = {
        bankId: this.bankId,
        questions: questionsToImport,
        allowDuplicate: this.importOptions.allowDuplicate,
        reverse: this.importOptions.reverse
      }

      batchImportQuestions(importData).then(response => {
        this.importing = false
        this.importResult = response.data || {
          successCount: questionsToImport.length,
          failCount: 0,
          errors: []
        }

        // 显示导入结果消息
        const { successCount, failCount } = this.importResult
        if (failCount > 0) {
          this.$message.warning(`导入完成：成功 ${successCount} 道，失败 ${failCount} 道题目`)
        } else {
          this.$message.success(`成功导入 ${successCount} 道题目`)
        }

        this.nextStep()
      }).catch(error => {
        this.importing = false
        console.error('导入数据失败', error)
        this.$message.error('导入数据失败: ' + (error.message || '未知错误'))
      })
    },
    // 完成导入
    handleComplete() {
      this.$emit('success')
      this.handleClose()
    },
    // 重置导入
    resetImport() {
      this.currentStep = 0
      this.importMode = this.defaultMode || 'excel'
      this.uploadedFile = null
      this.parsedData = []
      this.parseErrors = []
      this.importOptions = {
        reverse: false,
        allowDuplicate: false
      }
      this.importResult = {
        successCount: 0,
        failCount: 0,
        errors: []
      }
    },
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.import-container {
  padding: 20px 0;
}

.step-content {
  min-height: 400px;
}

.template-section h3,
.upload-section h3,
.preview-section h3 {
  margin-bottom: 10px;
  color: #333;
}

.template-buttons {
  margin: 20px 0;
  display: flex;
  gap: 15px;
}

.template-tips {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.template-tips h4 {
  margin-bottom: 10px;
  color: #333;
}

.template-tips ul {
  margin: 0;
  padding-left: 20px;
}

.template-tips li {
  margin-bottom: 5px;
  color: #666;
}

/* 文档导入样式 */
.import-mode-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.document-section {
  margin-top: 20px;
}

.document-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.document-format-tips {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.document-format-tips h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.format-rules {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.rule-item {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.rule-item h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.rule-item p {
  margin: 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.rule-item code {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #e74c3c;
}

.document-example {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.document-example h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.example-text {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  overflow-x: auto;
}

.upload-area {
  text-align: center;
  padding: 40px 0;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409EFF;
}

.upload-area i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 20px;
}

.upload-text p {
  margin: 0;
  color: #666;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.uploaded-file {
  margin-top: 15px;
}

.error-section {
  margin-bottom: 20px;
}

.error-list {
  max-height: 150px;
  overflow-y: auto;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 10px;
}

.error-item {
  color: #f56c6c;
  font-size: 14px;
  margin-bottom: 5px;
}

.table-tip {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 10px;
}

.result-section {
  text-align: center;
  padding: 40px 0;
}

.result-icon {
  margin-bottom: 20px;
}

.result-stats {
  margin: 20px 0;
}

.success-count {
  color: #67c23a;
  font-weight: bold;
  font-size: 18px;
}

.fail-count {
  color: #f56c6c;
  font-weight: bold;
  font-size: 18px;
}

.import-errors {
  margin-top: 20px;
  text-align: left;
}

.error-detail {
  color: #f56c6c;
  font-size: 14px;
  margin-bottom: 5px;
}

/* 导入选项样式 */
.import-options-section {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.import-options-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.import-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.import-options .el-checkbox {
  margin-right: 0;
  margin-bottom: 0;
}

.import-options .el-checkbox__label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.import-options .el-tooltip {
  cursor: help;
}

.step-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
